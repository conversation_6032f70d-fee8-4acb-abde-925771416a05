<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>iBox 功能演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .header p {
            color: #666;
            font-size: 14px;
        }

        .button-group {
            margin-bottom: 25px;
        }

        .group-title {
            color: #666;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 15px;
            padding-left: 5px;
        }

        .btn {
            width: 100%;
            padding: 15px 20px;
            margin-bottom: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            color: white;
        }

        .btn:hover {
            opacity: 0.9;
        }

        .btn-wechat {
            background: #1aad19;
        }

        .btn-share-friend {
            background: #07c160;
        }

        .btn-share-moments {
            background: #ffc107;
        }

        .btn-calendar {
            background: #2196f3;
        }

        .btn-new-window {
            background: #9c27b0;
        }

        .divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, #e0e0e0, transparent);
            margin: 20px 0;
        }

        .info-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }

        .info-section .info-item {
            margin-bottom: 8px;
        }

        .info-section .info-item:last-child {
            margin-bottom: 0;
        }

        .info-section strong {
            color: #333;
        }

        .info-section .info-item div {
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 3px 0;
        }

        .info-section .info-item div:last-child {
            margin-bottom: 0;
        }

        .info-section .info-item span {
            color: #666;
            font-family: monospace;
            font-size: 13px;
            max-width: 60%;
            word-break: break-all;
            text-align: right;
        }

        .result {
            margin-top: 20px;
            padding: 16px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            display: none;
            max-width: 100%;
            overflow-wrap: break-word;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .result.show {
            display: block;
        }

        /* 自定义确认对话框样式 */
        .confirm-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .confirm-dialog {
            background: white;
            border-radius: 12px;
            padding: 20px;
            max-width: 300px;
            width: 90%;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .confirm-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        .confirm-message {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.4;
        }

        .confirm-buttons {
            display: flex;
            gap: 10px;
        }

        .confirm-btn {
            flex: 1;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
        }

        .confirm-btn-cancel {
            background: #f5f5f5;
            color: #666;
        }

        .confirm-btn-danger {
            background: #ff4444;
            color: white;
        }

        .confirm-btn:hover {
            opacity: 0.9;
        }

        /* Token 管理样式 */
        .token-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .token-display {
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 12px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            min-height: 40px;
            display: flex;
            align-items: center;
        }

        .token-empty {
            color: #999;
            font-style: italic;
        }

        .token-value {
            color: #333;
        }

        .token-buttons {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }

        .btn-small {
            flex: 1;
            padding: 8px 12px;
            font-size: 14px;
            border-radius: 6px;
        }

        .btn-create {
            background: #28a745;
        }

        .btn-update {
            background: #ffc107;
            color: #333;
        }

        .btn-clear {
            background: #dc3545;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>iBox 功能演示</h1>
            <p>测试微信登录、分享和日历提醒功能</p>
        </div>

        <!-- 设备信息显示区域 -->
        <div class="button-group">
            <div class="group-title">📱 设备信息</div>
            <div class="info-section">
                <div class="info-item" id="device-info">
                    <div><strong>应用名称：</strong><span id="app-name">加载中...</span></div>
                    <div><strong>应用版本：</strong><span id="app-version">加载中...</span></div>
                    <div><strong>Bundle ID：</strong><span id="app-bundle-id">加载中...</span></div>
                    <div><strong>设备名称：</strong><span id="device-name">加载中...</span></div>
                    <div><strong>设备型号：</strong><span id="device-model">加载中...</span></div>
                    <div><strong>系统版本：</strong><span id="device-version">加载中...</span></div>
                    <div><strong>刘海高度：</strong><span id="device-bangs">加载中...</span></div>
                    <div><strong>屏幕尺寸：</strong><span id="screen-size">加载中...</span></div>
                    <div><strong>屏幕像素：</strong><span id="screen-pixel">加载中...</span></div>
                    <div><strong>屏幕倍率：</strong><span id="screen-scale">加载中...</span></div>
                    <div><strong>IDFA：</strong><span id="device-idfa">加载中...</span></div>
                    <div><strong>IDFV：</strong><span id="device-idfv">加载中...</span></div>
                </div>
            </div>
            <button class="btn btn-calendar" onclick="refreshDeviceInfo()">
                🔄 刷新设备信息
            </button>
        </div>

        <div class="divider"></div>

        <!-- 应用状态显示区域 -->
        <div class="button-group">
            <div class="group-title">🔄 应用状态</div>
            <div class="info-section">
                <div class="info-item" id="app-status">
                    <div><strong>应用状态：</strong><span id="app-state">活跃</span></div>
                    <div><strong>设备方向：</strong><span id="device-orientation">未知</span></div>
                    <div><strong>电池状态：</strong><span id="battery-state">未知</span></div>
                    <div><strong>电池电量：</strong><span id="battery-level">未知</span></div>
                    <div><strong>可用内存：</strong><span id="available-memory">未知</span></div>
                    <div><strong>最近事件：</strong><span id="latest-event">无</span></div>
                </div>
            </div>
            <button class="btn btn-calendar" onclick="requestCurrentStatus()">
                📊 获取当前状态
            </button>
        </div>

        <div class="divider"></div>

        <!-- Token 存储管理 -->
        <div class="button-group">
            <div class="group-title">🔑 Token 存储管理</div>

            <!-- localStorage Token -->
            <div class="token-section">
                <div style="font-weight: 500; color: #333; margin-bottom: 8px;">📦 LocalStorage Token</div>
                <div class="token-display" id="localStorage-token">
                    <span class="token-empty">暂无 Token</span>
                </div>
                <div class="token-buttons">
                    <button class="btn btn-small btn-create" onclick="createLocalStorageToken()">创建</button>
                    <button class="btn btn-small btn-update" onclick="updateLocalStorageToken()">更新</button>
                    <button class="btn btn-small btn-clear" onclick="clearLocalStorageToken()">清除</button>
                </div>
            </div>

            <!-- sessionStorage Token -->
            <div class="token-section">
                <div style="font-weight: 500; color: #333; margin-bottom: 8px;">🗂️ SessionStorage Token</div>
                <div class="token-display" id="sessionStorage-token">
                    <span class="token-empty">暂无 Token</span>
                </div>
                <div class="token-buttons">
                    <button class="btn btn-small btn-create" onclick="createSessionStorageToken()">创建</button>
                    <button class="btn btn-small btn-update" onclick="updateSessionStorageToken()">更新</button>
                    <button class="btn btn-small btn-clear" onclick="clearSessionStorageToken()">清除</button>
                </div>
            </div>
        </div>

        <div class="divider"></div>

        <!-- 微信登录 -->
        <div class="button-group">
            <div class="group-title">🔐 登录功能</div>
            <button class="btn btn-wechat" onclick="weixinLogin()">
                微信登录
            </button>
        </div>

        <!-- 微信分享 -->
        <div class="button-group">
            <div class="group-title">📤 分享功能</div>
            <button class="btn btn-share-friend" onclick="shareWebpageToFriend()">
                分享网页给好友
            </button>
            <button class="btn btn-share-moments" onclick="shareWebpageToMoments()">
                分享网页到朋友圈
            </button>
            <button class="btn btn-wechat" onclick="shareImageToFriend()">
                分享图片给好友
            </button>
            <button class="btn btn-calendar" onclick="shareImageToMoments()">
                分享图片到朋友圈
            </button>
        </div>

        <!-- 日历功能 -->
        <div class="button-group">
            <div class="group-title">📅 日历功能</div>
            <button class="btn btn-calendar" onclick="addCalendarReminder()">
                创建日历提醒
            </button>
            <button class="btn btn-share-friend" onclick="showCalendarList()">
                查看已创建日历 (<span id="calendar-count">0</span>)
            </button>
            <button class="btn btn-share-moments" onclick="deleteAllCalendars()">
                删除所有日历
            </button>
        </div>

        <!-- 日历列表显示区域 -->
        <div class="button-group" id="calendar-list" style="display: none;">
            <div class="group-title">📋 已创建的日历事件</div>
            <div class="info-section">
                <div id="calendar-items">
                    <!-- 日历项目将在这里动态添加 -->
                </div>
            </div>
        </div>

        <!-- 界面导航 -->
        <div class="button-group">
            <div class="group-title">🚀 界面导航</div>
            <button class="btn btn-new-window" onclick="openNewWindow()">
                打开新界面
            </button>
            <button class="btn btn-calendar" onclick="openSafariWithPrompt()">
                🌐 跳转到Safari
            </button>
            <button class="btn btn-calendar" onclick="openInAppSafariWithPrompt()">
                📱 打开app内Safari
            </button>
            <button class="btn btn-share-moments" onclick="closeCurrentWindow()">
                关闭当前界面
            </button>
        </div>

        <div id="result" class="result"></div>
    </div>

    <!-- 自定义确认对话框 -->
    <div id="confirm-overlay" class="confirm-overlay" onclick="handleOverlayClick(event)">
        <div class="confirm-dialog" onclick="event.stopPropagation()">
            <div class="confirm-title" id="confirm-title">确认操作</div>
            <div class="confirm-message" id="confirm-message">确定要执行此操作吗？</div>
            <div class="confirm-buttons">
                <button class="confirm-btn confirm-btn-cancel" onclick="hideConfirmDialog()">取消</button>
                <button class="confirm-btn confirm-btn-danger" id="confirm-ok-btn" onclick="confirmAction()">确定</button>
            </div>
        </div>
    </div>

    <!-- 引入iBox API -->
    <script src="ibox-api.js"></script>

    <script>
        // Token 管理功能
        const TOKEN_KEY = 'ibox_token';

        // 生成随机 Token
        function generateRandomToken() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let token = '';
            for (let i = 0; i < 32; i++) {
                token += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return token;
        }

        // 更新 Token 显示
        function updateTokenDisplay() {
            // 更新 localStorage Token 显示
            const localToken = localStorage.getItem(TOKEN_KEY);
            const localDisplay = document.getElementById('localStorage-token');
            if (localToken) {
                localDisplay.innerHTML = `<span class="token-value">${localToken}</span>`;
            } else {
                localDisplay.innerHTML = '<span class="token-empty">暂无 Token</span>';
            }

            // 更新 sessionStorage Token 显示
            const sessionToken = sessionStorage.getItem(TOKEN_KEY);
            const sessionDisplay = document.getElementById('sessionStorage-token');
            if (sessionToken) {
                sessionDisplay.innerHTML = `<span class="token-value">${sessionToken}</span>`;
            } else {
                sessionDisplay.innerHTML = '<span class="token-empty">暂无 Token</span>';
            }
        }

        // LocalStorage Token 操作
        function createLocalStorageToken() {
            const token = generateRandomToken();
            localStorage.setItem(TOKEN_KEY, token);
            updateTokenDisplay();
            showResult('LocalStorage Token 创建成功', `新 Token: ${token}`);
        }

        function updateLocalStorageToken() {
            const existingToken = localStorage.getItem(TOKEN_KEY);
            if (!existingToken) {
                showResult('更新失败', 'LocalStorage 中没有现有的 Token，请先创建');
                return;
            }
            const newToken = generateRandomToken();
            localStorage.setItem(TOKEN_KEY, newToken);
            updateTokenDisplay();
            showResult('LocalStorage Token 更新成功', `旧 Token: ${existingToken}\n新 Token: ${newToken}`);
        }

        function clearLocalStorageToken() {
            const existingToken = localStorage.getItem(TOKEN_KEY);
            if (!existingToken) {
                showResult('清除失败', 'LocalStorage 中没有 Token');
                return;
            }
            localStorage.removeItem(TOKEN_KEY);
            updateTokenDisplay();
            showResult('LocalStorage Token 清除成功', `已清除 Token: ${existingToken}`);
        }

        // SessionStorage Token 操作
        function createSessionStorageToken() {
            const token = generateRandomToken();
            sessionStorage.setItem(TOKEN_KEY, token);
            updateTokenDisplay();
            showResult('SessionStorage Token 创建成功', `新 Token: ${token}`);
        }

        function updateSessionStorageToken() {
            const existingToken = sessionStorage.getItem(TOKEN_KEY);
            if (!existingToken) {
                showResult('更新失败', 'SessionStorage 中没有现有的 Token，请先创建');
                return;
            }
            const newToken = generateRandomToken();
            sessionStorage.setItem(TOKEN_KEY, newToken);
            updateTokenDisplay();
            showResult('SessionStorage Token 更新成功', `旧 Token: ${existingToken}\n新 Token: ${newToken}`);
        }

        function clearSessionStorageToken() {
            const existingToken = sessionStorage.getItem(TOKEN_KEY);
            if (!existingToken) {
                showResult('清除失败', 'SessionStorage 中没有 Token');
                return;
            }
            sessionStorage.removeItem(TOKEN_KEY);
            updateTokenDisplay();
            showResult('SessionStorage Token 清除成功', `已清除 Token: ${existingToken}`);
        }

        // 页面功能函数 - 调用iBox API
        function weixinLogin() {
            iBoxAPI.weixinLogin();
        }

        function shareWebpageToFriend() {
            iBoxAPI.shareToWeixin({
                type: 'webpage',
                to: '0',
                title: '测试网页分享',
                content: '这是一个测试网页分享内容',
                imageUrl: 'https://www.xxgame.cn/images/banner_fj.jpg',
                mediaUrl: 'https://www.xxgame.cn'
            });
            showResult('分享网页给好友', '正在调用微信分享...');
        }

        function shareWebpageToMoments() {
            iBoxAPI.shareToWeixin({
                type: 'webpage',
                to: '1',
                title: '测试网页分享',
                content: '这是一个测试网页分享内容',
                imageUrl: 'https://www.xxgame.cn/images/banner_fj.jpg',
                mediaUrl: 'https://www.xxgame.cn'
            });
            showResult('分享网页到朋友圈', '正在调用微信分享...');
        }

        function shareImageToFriend() {
            iBoxAPI.shareToWeixin({
                type: 'image',
                to: '0',
                title: '测试图片分享',
                content: '这是一个测试图片分享',
                imageUrl: 'https://www.xxgame.cn/images/banner_fj.jpg'
            });
            showResult('分享图片给好友', '正在调用微信分享...');
        }

        function shareImageToMoments() {
            iBoxAPI.shareToWeixin({
                type: 'image',
                to: '1',
                title: '测试图片分享',
                content: '这是一个测试图片分享',
                imageUrl: 'https://www.xxgame.cn/images/banner_fj.jpg'
            });
            showResult('分享图片到朋友圈', '正在调用微信分享...');
        }

        function addCalendarReminder() {
            // 获取用户输入或使用默认值
            const title = prompt('请输入日历标题：', '重要提醒') || '重要提醒';
            const content = prompt('请输入日历内容：', '这是一个测试提醒事件') || '这是一个测试提醒事件';
            const location = prompt('请输入地点（可为空）：', '') || '';
            const repeatRule = prompt('请输入重复规则（daily/weekly/monthly/yearly/every_2_days，可为空）：', '') || '';
            const repeatCount = parseInt(prompt('请输入重复次数（0表示使用结束日期）：', '0') || '0');
            const reminderMinutes = parseInt(prompt('请输入提醒提前时间（分钟，0表示不设置）：', '15') || '0');

            // 设置开始时间为1小时后
            const startDate = new Date(Date.now() + 60 * 60 * 1000);
            // 设置结束时间为开始时间后1小时
            const endDate = new Date(startDate.getTime() + 60 * 60 * 1000);

            // 显示时间调试信息
            const now = new Date();
            console.log('当前时间:', now.toLocaleString());
            console.log('开始时间:', startDate.toLocaleString());
            console.log('结束时间:', endDate.toLocaleString());

            showResult('日历提醒创建中...',
                `当前时间: ${now.toLocaleString()}<br/>` +
                `开始时间: ${startDate.toLocaleString()}<br/>` +
                `结束时间: ${endDate.toLocaleString()}<br/>` +
                `标题: ${title}`
            );

            iBoxAPI.addCalendarReminder({
                title: title,
                content: content,
                startDate: startDate,
                endDate: endDate,
                alertOffset: -900, // 提前15分钟提醒
                location: location,
                repeatRule: repeatRule,
                repeatCount: repeatCount,
                reminderMinutes: reminderMinutes
            });
        }

        function refreshDeviceInfo() {
            // 重置显示状态为加载中
            document.getElementById('app-name').textContent = '加载中...';
            document.getElementById('app-version').textContent = '加载中...';
            document.getElementById('app-bundle-id').textContent = '加载中...';
            document.getElementById('device-name').textContent = '加载中...';
            document.getElementById('device-model').textContent = '加载中...';
            document.getElementById('device-version').textContent = '加载中...';
            document.getElementById('device-bangs').textContent = '加载中...';
            document.getElementById('screen-size').textContent = '加载中...';
            document.getElementById('screen-pixel').textContent = '加载中...';
            document.getElementById('screen-scale').textContent = '加载中...';
            document.getElementById('device-idfa').textContent = '加载中...';
            document.getElementById('device-idfv').textContent = '加载中...';

            // 重置应用状态显示
            document.getElementById('battery-state').textContent = '加载中...';
            document.getElementById('battery-level').textContent = '加载中...';
            document.getElementById('available-memory').textContent = '加载中...';

            // 调用获取设备信息API
            iBoxAPI.getDeviceInfo();
            showResult('正在获取设备信息...', '请稍候');
        }

        function requestCurrentStatus() {
            // 手动触发获取当前状态
            showResult('正在获取当前状态...', '包括电池、方向、内存信息');

            // 调用专门的获取当前状态API
            iBoxAPI.getCurrentStatus();
        }

        function openNewWindow() {
            // 测试打开新界面功能
            const testUrl = 'https://www.baidu.com';
            showResult('正在打开新界面...', `目标URL: ${testUrl}`);

            // 使用window.open触发原生WKUIDelegate方法
            window.open(testUrl, '_blank');
        }

        function closeCurrentWindow() {
            // 关闭当前界面，添加确认对话框
            showConfirmDialog(
                '关闭界面',
                '确定要关闭当前界面吗？如果是主界面则无法关闭。',
                function () {
                    showResult('正在关闭界面...', '如果当前不是模态界面，此操作无效');
                    iBoxAPI.closeCurrentWindow();
                }
            );
        }

        function openSafariWithPrompt() {
            // 提示用户输入要打开的URL
            const url = prompt('请输入要在Safari中打开的网址：', 'https://www.baidu.com');
            if (url && url.trim() !== '') {
                const targetUrl = url.trim();
                showResult('正在跳转到Safari...', `目标URL: ${targetUrl}`);
                iBoxAPI.openURL(targetUrl);
            }
        }

        function openInAppSafariWithPrompt() {
            // 提示用户输入要打开的URL
            const url = prompt('请输入要在app内Safari中打开的网址：', 'https://www.baidu.com');
            if (url && url.trim() !== '') {
                const targetUrl = url.trim();
                showResult('正在打开app内Safari...', `目标URL: ${targetUrl}`);
                iBoxAPI.openInAppSafari(targetUrl);
            }
        }

        // 获取并显示设备信息
        function displayDeviceInfo() {
            // 使用API获取设备信息
            iBoxAPI.getDeviceInfo();
        }

        // 处理微信登录回调
        function handleWeixinLogin(result) {
            if (result.success) {
                showResult('微信登录成功！', `授权码: ${result.code}<br>微信ID: ${result.wx_id}`);
                // TODO: 微信登录请求校验
            } else {
                showResult('微信登录失败', result.error || '未知错误');
            }
        }

        // 日历存储管理（基于OC本地存储）
        let calendarEvents = [];
        let isCalendarListVisible = false;

        // 更新日历计数显示
        function updateCalendarCount() {
            document.getElementById('calendar-count').textContent = calendarEvents.length;
        }

        // 从OC获取最新的日历列表
        function refreshCalendarData() {
            iBoxAPI.getAllCalendarEvents();
        }

        // 处理日历回调
        function handleCalendar(callbackData) {
            if (callbackData.success) {
                showResult('日历提醒创建成功！',
                    `标题: ${callbackData.title}\n事件ID: ${callbackData.eventId}\n已自动保存到本地`);

                // 刷新日历数据
                refreshCalendarData();
            } else {
                showResult('日历提醒创建失败', callbackData.error || '无法添加到系统日历');
            }
        }

        // 处理删除日历回调
        function handleDeleteCalendar(callbackData) {
            if (callbackData.success) {
                if (callbackData.eventId === 'all') {
                    showResult('删除成功！', '所有日历事件已删除');
                } else {
                    showResult('删除成功！', `事件ID: ${callbackData.eventId} 已删除`);
                }
                // 刷新日历数据
                refreshCalendarData();
            } else {
                showResult('删除失败', callbackData.error || '无法删除日历事件');
            }
        }

        // 处理获取所有日历回调
        function handleGetAllCalendars(callbackData) {
            if (callbackData.success) {
                calendarEvents = callbackData.events || [];
                updateCalendarCount();

                // 如果日历列表是可见的，刷新显示
                if (isCalendarListVisible) {
                    refreshCalendarList();
                }

                console.log('已获取到', calendarEvents.length, '个日历事件');
            } else {
                console.error('获取日历事件失败:', callbackData.error);
                calendarEvents = [];
                updateCalendarCount();
            }
        }

        // 显示/隐藏日历列表
        function showCalendarList() {
            const listElement = document.getElementById('calendar-list');
            if (listElement.style.display === 'none') {
                isCalendarListVisible = true;
                // 从OC获取最新数据并显示
                refreshCalendarData();
                listElement.style.display = 'block';
            } else {
                isCalendarListVisible = false;
                listElement.style.display = 'none';
            }
        }

        // 刷新日历列表显示
        function refreshCalendarList() {
            const container = document.getElementById('calendar-items');
            if (calendarEvents.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">暂无日历事件</div>';
                return;
            }

            let html = '';
            calendarEvents.forEach((event, index) => {
                const startDate = new Date(event.startDate * 1000).toLocaleString();
                const createDate = new Date(event.createTime * 1000).toLocaleString();
                const notes = event.notes || '无备注';

                html += `
                    <div style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 15px; margin-bottom: 10px;">
                        <div style="font-weight: 600; color: #333; margin-bottom: 8px;">${event.title}</div>
                        <div style="font-size: 12px; color: #666; margin-bottom: 4px;">开始时间: ${startDate}</div>
                        <div style="font-size: 12px; color: #666; margin-bottom: 4px;">创建时间: ${createDate}</div>
                        <div style="font-size: 12px; color: #666; margin-bottom: 4px;">备注: ${notes}</div>
                        <div style="font-size: 11px; color: #999; margin-bottom: 10px;">ID: ${event.eventId}</div>
                        <button onclick="deleteSpecificCalendar('${event.eventId}')"
                                style="background: #ff4444; color: white; border: none; padding: 5px 10px; border-radius: 4px; font-size: 12px;">
                            删除此事件
                        </button>
                    </div>
                `;
            });
            container.innerHTML = html;
        }

        // 自定义确认对话框变量
        let confirmCallback = null;

        // 显示自定义确认对话框
        function showConfirmDialog(title, message, callback) {
            document.getElementById('confirm-title').textContent = title;
            document.getElementById('confirm-message').textContent = message;
            document.getElementById('confirm-overlay').style.display = 'flex';
            confirmCallback = callback;
        }

        // 隐藏确认对话框
        function hideConfirmDialog() {
            document.getElementById('confirm-overlay').style.display = 'none';
            confirmCallback = null;
        }

        // 确认操作
        function confirmAction() {
            if (confirmCallback && typeof confirmCallback === 'function') {
                confirmCallback();
            }
            hideConfirmDialog();
        }

        // 处理点击对话框外部区域
        function handleOverlayClick(event) {
            if (event.target.id === 'confirm-overlay') {
                hideConfirmDialog();
            }
        }

        // 删除指定日历事件
        function deleteSpecificCalendar(eventId) {
            showConfirmDialog(
                '删除日历事件',
                '确定要删除这个日历事件吗？',
                function () {
                    iBoxAPI.deleteCalendarEvent(eventId);
                }
            );
        }

        // 删除所有日历事件
        function deleteAllCalendars() {
            if (calendarEvents.length === 0) {
                showResult('提示', '当前没有可删除的日历事件');
                return;
            }

            showConfirmDialog(
                '删除所有日历事件',
                `确定要删除所有 ${calendarEvents.length} 个日历事件吗？此操作不可撤销！`,
                function () {
                    iBoxAPI.deleteAllCalendarEvents();
                }
            );
        }

        // 处理设备信息
        function handleDeviceInfo(deviceInfo) {
            if (deviceInfo && typeof deviceInfo === 'object') {
                // 更新各个设备信息字段
                document.getElementById('app-name').textContent = deviceInfo.app_name || '未知';
                document.getElementById('app-version').textContent = deviceInfo.app_version || '未知';
                document.getElementById('app-bundle-id').textContent = deviceInfo.app_bundle_id || '未知';
                document.getElementById('device-name').textContent = deviceInfo.device_name || '未知';
                document.getElementById('device-model').textContent = deviceInfo.device_model || '未知';
                document.getElementById('device-version').textContent = deviceInfo.device_version || '未知';
                document.getElementById('device-bangs').textContent = deviceInfo.device_bangs + 'px' || '未知';
                document.getElementById('screen-size').textContent = deviceInfo.screen_size + 'pt' || '未知';
                document.getElementById('screen-pixel').textContent = deviceInfo.screen_pixel + 'px' || '未知';
                document.getElementById('screen-scale').textContent = deviceInfo.screen_scale + 'x' || '未知';
                document.getElementById('device-idfa').textContent = deviceInfo.device_idfa || '未知';
                document.getElementById('device-idfv').textContent = deviceInfo.device_idfv || '未知';

                // 更新应用状态区域的信息
                if (deviceInfo.battery_state) {
                    const batteryStateMap = {
                        'unplugged': '未插电',
                        'charging': '充电中',
                        'full': '已充满',
                        'unknown': '未知'
                    };
                    document.getElementById('battery-state').textContent = batteryStateMap[deviceInfo.battery_state] || deviceInfo.battery_state;
                }

                if (deviceInfo.battery_level) {
                    document.getElementById('battery-level').textContent = deviceInfo.battery_level;
                }

                if (deviceInfo.available_memory) {
                    document.getElementById('available-memory').textContent = deviceInfo.available_memory;
                }

                console.log('设备信息已更新:', deviceInfo);
            } else {
                console.error('设备信息无效或获取失败');
                // 显示错误信息
                const errorMessage = '获取失败';
                document.getElementById('app-name').textContent = errorMessage;
                document.getElementById('app-version').textContent = errorMessage;
                document.getElementById('app-bundle-id').textContent = errorMessage;
                document.getElementById('device-name').textContent = errorMessage;
                document.getElementById('device-model').textContent = errorMessage;
                document.getElementById('device-version').textContent = errorMessage;
                document.getElementById('device-bangs').textContent = errorMessage;
                document.getElementById('screen-size').textContent = errorMessage;
                document.getElementById('screen-pixel').textContent = errorMessage;
                document.getElementById('screen-scale').textContent = errorMessage;
                document.getElementById('device-idfa').textContent = errorMessage;
                document.getElementById('device-idfv').textContent = errorMessage;

                // 重置应用状态显示为错误状态
                document.getElementById('battery-state').textContent = errorMessage;
                document.getElementById('battery-level').textContent = errorMessage;
                document.getElementById('available-memory').textContent = errorMessage;
            }
        }

        // 处理生命周期事件
        function handleLifecycleEvent(eventData) {
            if (!eventData || !eventData.event_type) {
                return;
            }

            const eventType = eventData.event_type;
            const data = eventData.data;

            console.log('收到生命周期事件:', eventType, data);

            // 更新最近事件
            const now = new Date().toLocaleTimeString();
            document.getElementById('latest-event').textContent = `${eventType} (${now})`;

            // 根据事件类型更新相应的状态
            switch (eventType) {
                case 'active':
                    document.getElementById('app-state').textContent = '活跃';
                    break;
                case 'inactive':
                    document.getElementById('app-state').textContent = '非活跃';
                    break;
                case 'background':
                    document.getElementById('app-state').textContent = '后台';
                    break;
                case 'foreground':
                    document.getElementById('app-state').textContent = '前台';
                    break;
                case 'memory_warning':
                    document.getElementById('app-state').textContent = '内存警告';
                    if (data.available_memory) {
                        const memoryMB = Math.round(data.available_memory / 1024 / 1024);
                        document.getElementById('available-memory').textContent = `${memoryMB} MB`;
                    }
                    break;
                case 'orientation_change':
                    if (data.orientation) {
                        const orientationMap = {
                            'portrait': '竖屏',
                            'portrait_upside_down': '倒立竖屏',
                            'landscape_left': '左横屏',
                            'landscape_right': '右横屏',
                            'face_up': '平放向上',
                            'face_down': '平放向下',
                            'unknown': '未知'
                        };
                        document.getElementById('device-orientation').textContent = orientationMap[data.orientation] || data.orientation;

                        // 方向变化时重新获取设备信息，延迟确保变化完成
                        console.log('设备方向变化，延迟获取最新设备信息...');
                        setTimeout(function () {
                            displayDeviceInfo();
                            console.log('方向变化后已重新获取设备信息');
                        }, 300);
                    }
                    break;
                case 'battery_state_change':
                    if (data.battery_state) {
                        const batteryStateMap = {
                            'unplugged': '未插电',
                            'charging': '充电中',
                            'full': '已充满',
                            'unknown': '未知'
                        };
                        document.getElementById('battery-state').textContent = batteryStateMap[data.battery_state] || data.battery_state;
                    }
                    if (data.battery_level !== undefined) {
                        const level = Math.round(data.battery_level * 100);
                        document.getElementById('battery-level').textContent = `${level}%`;
                    }
                    break;
                case 'battery_level_change':
                    if (data.battery_level !== undefined) {
                        const level = Math.round(data.battery_level * 100);
                        document.getElementById('battery-level').textContent = `${level}%`;
                    }
                    break;
                case 'memory_info':
                    if (data.available_memory) {
                        const memoryMB = Math.round(data.available_memory / 1024 / 1024);
                        document.getElementById('available-memory').textContent = `${memoryMB} MB`;
                    }
                    break;
            }

            // 在结果区域显示事件详情
            showResult(`生命周期事件: ${eventType}`, JSON.stringify(data, null, 2));
        }

        // 显示操作结果
        function showResult(message, details) {
            const resultDiv = document.getElementById('result');
            if (!resultDiv) return;

            if (details) {
                resultDiv.innerHTML = `
                    <div style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 6px;">${message}</div>
                    <div style="font-size: 14px; color: #666; word-break: break-all; word-wrap: break-word; white-space: pre-wrap; line-height: 1.4;">${details}</div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div style="font-size: 16px; font-weight: 600; color: #333;">${message}</div>
                `;
            }

            resultDiv.classList.add('show');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            console.log('iBox 演示页面已加载');

            // 设置微信登录回调
            iBoxAPI.setLoginCallback(function (result) {
                handleWeixinLogin(result);
            });

            // 设置日历回调
            iBoxAPI.setCalendarCallback(function (callbackData) {
                handleCalendar(callbackData);
            });

            // 设置删除日历回调
            iBoxAPI.setDeleteCalendarCallback(function (callbackData) {
                handleDeleteCalendar(callbackData);
            });

            // 设置获取所有日历回调
            iBoxAPI.setGetAllCalendarsCallback(function (callbackData) {
                handleGetAllCalendars(callbackData);
            });

            // 设置设备信息回调
            iBoxAPI.setDeviceInfoCallback(function (result) {
                handleDeviceInfo(result);
            });

            // 设置生命周期事件回调
            iBoxAPI.setLifecycleCallback(function (eventData) {
                handleLifecycleEvent(eventData);
            });

            // 初始化数据（现在使用队列机制避免URL scheme竞争条件）
            // 显示设备信息
            displayDeviceInfo();

            // 初始化日历数据
            refreshCalendarData();
        });
    </script>
</body>

</html>
