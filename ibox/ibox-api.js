/**
 * iBox API - 提供微信登录、分享、日历提醒等功能的JavaScript接口
 * 版本: 1.0.0
 * 作者: zhmbo
 */

// iBox API 命名空间
window.iBoxAPI = (function() {
    'use strict';

    // 私有变量
    let loginCallback = null;
    let calendarCallback = null;
    let deleteCalendarCallback = null;
    let getAllCalendarsCallback = null;
    let getDeviceInfoCallback = null;
    let lifecycleCallback = null;

    // URL调用队列管理
    let urlQueue = [];
    let isProcessingQueue = false;

    // 私有方法 - 处理URL调用队列
    function processUrlQueue() {
        if (isProcessingQueue || urlQueue.length === 0) {
            return;
        }

        isProcessingQueue = true;
        const urlCall = urlQueue.shift();

        console.log('执行URL调用:', urlCall.url);
        window.location.href = urlCall.url;

        // 设置延时，确保当前调用完成后再处理下一个
        setTimeout(() => {
            isProcessingQueue = false;
            processUrlQueue(); // 处理队列中的下一个调用
        }, urlCall.delay || 100); // 默认延时100ms
    }

    // 私有方法 - 添加URL调用到队列
    function queueUrlCall(url, delay = 100) {
        urlQueue.push({ url: url, delay: delay });
        processUrlQueue();
    }

    // 私有方法 - 直接执行URL调用（用于不需要排队的场景）
    function executeUrlCall(url) {
        console.log('直接执行URL调用:', url);
        window.location.href = url;
    }

    // 私有方法 - 读取URL参数
    function getUrlParams() {
        const params = {};
        const urlSearchParams = new URLSearchParams(window.location.search);

        for (const [key, value] of urlSearchParams) {
            params[key] = value;
        }

        return params;
    }


    // 公共API
    return {
        /**
         * 版本信息
         */
        version: '1.0.4',

        /**
         * 微信登录
         * @param {string} wxId - 微信应用ID，默认为 'wx1f02537b5962f1fc'
         */
        weixinLogin: function(wxId = 'wx1f02537b5962f1fc') {
            const url = `ibox://weixin_login?wx_id=${wxId}`;
            executeUrlCall(url); // 登录不需要排队，立即执行
        },

        /**
         * 分享到微信
         * @param {Object} options - 分享参数
         * @param {string} options.type - 分享类型：'webpage'网页链接, 'image'图片
         * @param {string} options.to - 分享目标：'0'好友, '1'朋友圈
         * @param {string} options.title - 分享标题
         * @param {string} options.content - 分享内容
         * @param {string} options.imageUrl - 分享图片URL
         * @param {string} options.mediaUrl - 分享链接URL（type为webpage时必需）
         * @param {string} options.wxId - 微信应用ID
         */
        shareToWeixin: function(options = {}) {
            const params = {
                type: options.type || 'webpage',
                to: options.to || '0',
                title: options.title || '测试标题',
                content: options.content || '测试内容',
                image_url: options.imageUrl || 'https://www.xxgame.cn/images/banner_fj.jpg',
                wx_id: options.wxId || 'wx1f02537b5962f1fc'
            };

            // 只有webpage类型才需要media_url
            if (params.type === 'webpage') {
                params.media_url = options.mediaUrl || 'https://www.xxgame.cn';
            }

            const url = `ibox://weixin_share?${new URLSearchParams(params).toString()}`;
            executeUrlCall(url); // 分享操作立即执行，不排队
        },

        /**
         * 分享给微信好友（兼容方法）
         * @param {Object} options - 分享参数
         */
        shareToFriend: function(options = {}) {
            this.shareToWeixin({
                ...options,
                to: '0'
            });
        },

        /**
         * 分享到微信朋友圈（兼容方法）
         * @param {Object} options - 分享参数
         */
        shareToMoments: function(options = {}) {
            this.shareToWeixin({
                ...options,
                to: '1'
            });
        },

        /**
         * 创建日历提醒
         * @param {Object} options - 提醒参数
         * @param {string} options.title - 提醒标题
         * @param {string} options.content - 提醒内容
         * @param {Date|string} options.startDate - 开始时间
         * @param {Date|string} options.endDate - 结束时间
         * @param {number} options.alertOffset - 提醒偏移时间（秒），负数表示提前
         * @param {string} options.location - 地点
         * @param {string} options.repeatRule - 重复规则（daily/weekly/monthly/yearly/every_N_days等）
         * @param {number} options.repeatCount - 重复次数
         * @param {number} options.reminderMinutes - 额外提醒时间（分钟），正数表示提前多少分钟提醒
         */
        addCalendarReminder: function(options = {}) {
            const now = new Date();
            let startDate = options.startDate;
            let endDate = options.endDate;

            // 处理默认时间
            if (!startDate) {
                startDate = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 明天
            } else if (typeof startDate === 'string') {
                startDate = new Date(startDate);
            }

            if (!endDate) {
                endDate = new Date(startDate.getTime() + 60 * 60 * 1000); // 持续1小时
            } else if (typeof endDate === 'string') {
                endDate = new Date(endDate);
            }

            // 将Date对象转换为本地时间字符串格式 (YYYY-MM-DDTHH:mm:ss)
            function toLocalDateTimeString(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');
                return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
            }

            // 构建参数对象，只包含非空值
            const params = {
                title: options.title || '重要提醒',
                content: options.content || '这是一个测试提醒事件',
                start_date: toLocalDateTimeString(startDate),
                end_date: toLocalDateTimeString(endDate),
                alert_offset: options.alertOffset || -900,
                location: options.location || '',
                repeat_rule: options.repeatRule || '',
                repeat_count: options.repeatCount || 0,
                reminder_minutes: options.reminderMinutes || 0
            };

            const url = `ibox://calendar_reminder?${new URLSearchParams(params).toString()}`;
            executeUrlCall(url); // 日历创建立即执行，不排队
        },

        /**
         * 获取设备信息
         */
        getDeviceInfo: function() {
            const url = `ibox://device_info`;
            queueUrlCall(url, 150); // 使用队列机制，延时150ms
        },

        /**
         * 获取当前状态（电池、方向、内存等实时状态）
         */
        getCurrentStatus: function() {
            const url = `ibox://current_status`;
            queueUrlCall(url, 100); // 使用队列机制
        },

        /**
         * 删除指定日历事件
         * @param {string} eventId - 事件ID
         */
        deleteCalendarEvent: function(eventId) {
            if (!eventId) {
                console.error('删除日历事件失败：事件ID不能为空');
                return;
            }
            const url = `ibox://delete_calendar?event_id=${encodeURIComponent(eventId)}`;
            queueUrlCall(url, 150); // 使用队列机制，延时150ms
        },

        /**
         * 删除所有日历事件
         */
        deleteAllCalendarEvents: function() {
            const url = `ibox://delete_calendar?all=true`;
            queueUrlCall(url, 150); // 使用队列机制，延时150ms
        },

        /**
         * 获取所有已保存的日历事件
         */
        getAllCalendarEvents: function() {
            const url = `ibox://get_all_calendars`;
            queueUrlCall(url, 150); // 使用队列机制，延时150ms
        },

        /**
         * 跳转到Safari浏览器
         * @param {string} targetUrl - 要打开的URL地址
         */
        openURL: function(targetUrl) {
            if (!targetUrl) {
                console.error('openURL失败：目标URL不能为空');
                return;
            }

            // 确保URL格式正确
            let url = targetUrl;
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                url = 'https://' + url;
            }

            const encodedUrl = encodeURIComponent(url);
            const iboxUrl = `ibox://openURL?url=${encodedUrl}`;
            executeUrlCall(iboxUrl); // 跳转操作立即执行，不排队
        },

        /**
         * 跳转到Safari浏览器（别名方法）
         * @param {string} targetUrl - 要打开的URL地址
         */
        openSafari: function(targetUrl) {
            this.openURL(targetUrl);
        },

        /**
         * 在app内打开Safari浏览器
         * @param {string} targetUrl - 要打开的URL地址
         */
        openInAppSafari: function(targetUrl) {
            if (!targetUrl) {
                console.error('openInAppSafari失败：目标URL不能为空');
                return;
            }

            // 确保URL格式正确
            let url = targetUrl;
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                url = 'https://' + url;
            }

            const encodedUrl = encodeURIComponent(url);
            const iboxUrl = `ibox://openInAppSafari?url=${encodedUrl}`;
            executeUrlCall(iboxUrl); // 跳转操作立即执行，不排队
        },

        /**
         * 关闭当前界面
         */
        closeCurrentWindow: function() {
            const url = `ibox://close_window`;
            executeUrlCall(url); // 关闭操作立即执行，不排队
        },

        /**
         * 获取URL参数
         * @returns {Object} URL参数对象
         */
        getUrlParams: function() {
            return getUrlParams();
        },

        /**
         * 获取IDFA信息
         * @returns {Object} IDFA信息对象
         */
        getIdfaInfo: function() {
            const params = getUrlParams();
            const fullUrl = window.location.href;
            const idfaValue = params['idfa'];
            const bangsValue = params['bangs'];

            return {
                idfa: idfaValue || null,
                bangs:bangsValue || null,
                fullUrl: fullUrl,
                allParams: params,
                hasIdfa: !!idfaValue
            };
        },

        /**
         * 设置微信登录回调处理函数
         * @param {Function} callback - 回调函数，参数为回调数据对象
         */
        setLoginCallback: function(callback) {
            loginCallback = callback;
        },

        /**
         * 设置日历回调处理函数
         * @param {Function} callback - 回调函数，参数为日历操作结果对象
         */
        setCalendarCallback: function(callback) {
            calendarCallback = callback;
        },

        /**
         * 设置删除日历回调处理函数
         * @param {Function} callback - 回调函数，参数为删除操作结果对象
         */
        setDeleteCalendarCallback: function(callback) {
            deleteCalendarCallback = callback;
        },

        /**
         * 设置获取所有日历回调处理函数
         * @param {Function} callback - 回调函数，参数为所有日历事件数据
         */
        setGetAllCalendarsCallback: function(callback) {
            getAllCalendarsCallback = callback;
        },

        /**
         * 设置设备信息回调处理函数
         * @param {Function} callback - 回调函数，参数为设备信息对象
         */
        setDeviceInfoCallback: function(callback) {
            getDeviceInfoCallback = callback;
        },

        /**
         * 设置生命周期事件回调处理函数
         * @param {Function} callback - 回调函数，参数为生命周期事件数据
         */
        setLifecycleCallback: function(callback) {
            lifecycleCallback = callback;
        },

        /**
         * 处理微信登录回调（由原生代码调用）
         * @param {string} jsonString - 回调数据JSON字符串
         */
        handleWeixinLoginCallback: function(jsonString) {
            try {
                const callbackData = JSON.parse(jsonString);
                console.log('收到微信登录回调:', callbackData);

                // 从URL中解析wx_id和code
                // URL格式: wx1f02537b5962f1fc://oauth?code=071bEDll23VBTf4sfdol2tMd5i2bEDl3&state=wxAuth
                let wx_id = null;
                let code = null;

                if (callbackData.url) {
                    // 解析URL获取wx_id (scheme部分)
                    const urlMatch = callbackData.url.match(/^([^:]+):\/\//);
                    if (urlMatch) {
                        wx_id = urlMatch[1];
                    }

                    // 解析URL参数获取code
                    const urlObj = new URL(callbackData.url);
                    code = urlObj.searchParams.get('code');
                }

                // 如果没有从URL解析到，尝试从callbackData直接获取
                if (!wx_id) wx_id = callbackData.wx_id || null;
                if (!code) code = callbackData.code || null;

                const result = {
                    wx_id: wx_id,
                    code: code,
                    success: !!code,
                    error: callbackData.error || null
                };

                console.log('解析后的登录数据:', result);

                // 触发登录回调
                if (loginCallback && typeof loginCallback === 'function') {
                    loginCallback(result);
                }

            } catch (error) {
                console.error('解析微信登录回调数据失败:', error);
                // 触发登录回调，传递错误信息
                if (loginCallback && typeof loginCallback === 'function') {
                    loginCallback({
                        wx_id: null,
                        code: null,
                        success: false,
                        error: error.message
                    });
                }
            }
        },

        /**
         * 处理日历回调（由原生代码调用）
         * @param {object} callbackData - 日历回调数据对象
         */
        handleCalendarCallback: function(callbackData) {
            console.log('收到日历回调:', callbackData);

            // 触发日历回调
            if (calendarCallback && typeof calendarCallback === 'function') {
                calendarCallback(callbackData);
            }
        },

        /**
         * 处理删除日历回调（由原生代码调用）
         * @param {object} callbackData - 删除日历回调数据对象
         */
        handleDeleteCalendarCallback: function(callbackData) {
            console.log('收到删除日历回调:', callbackData);

            // 触发删除日历回调
            if (deleteCalendarCallback && typeof deleteCalendarCallback === 'function') {
                deleteCalendarCallback(callbackData);
            }
        },

        /**
         * 处理设备信息回调（由原生代码调用）
         * @param {string|object} data - 设备信息JSON字符串或对象
         */
        handleDeviceInfoCallback: function(data) {
            try {
                // 兼容字符串和对象两种传递方式
                let callbackData;
                if (typeof data === 'string') {
                    callbackData = JSON.parse(data);
                } else if (typeof data === 'object' && data !== null) {
                    callbackData = data;
                } else {
                    throw new Error('Invalid data type');
                }

                console.log('收到设备信息回调:', callbackData);

                const result = {
                    app_bundle_id: callbackData.app_bundle_id,
                    app_version: callbackData.app_version,
                    app_name: callbackData.app_name,
                    device_name: callbackData.device_name,
                    device_idfa: callbackData.device_idfa,
                    device_idfv: callbackData.device_idfv,
                    device_model: callbackData.device_model,
                    device_version: callbackData.device_version,
                    device_bangs: callbackData.device_bangs,
                    screen_size: callbackData.screen_size,
                    screen_pixel: callbackData.screen_pixel,
                    screen_scale: callbackData.screen_scale,
                    battery_state: callbackData.battery_state,
                    battery_level: callbackData.battery_level,
                    available_memory: callbackData.available_memory,
                };

                console.log('解析后的设备信息数据:', result);

                // 触发设备信息回调
                if (getDeviceInfoCallback && typeof getDeviceInfoCallback === 'function') {
                    getDeviceInfoCallback(result);
                }

            } catch (error) {
                console.error('解析设备信息回调数据失败:', error, '原始数据:', data);
                // 触发设备信息回调，传递错误信息
                if (getDeviceInfoCallback && typeof getDeviceInfoCallback === 'function') {
                    getDeviceInfoCallback(null);
                }
            }
                 },

        /**
         * 处理获取所有日历回调（由原生代码调用）
         * @param {object} callbackData - 所有日历事件数据对象
         */
        handleGetAllCalendarsCallback: function(callbackData) {
            console.log('收到获取所有日历回调:', callbackData);

            // 触发获取所有日历回调
            if (getAllCalendarsCallback && typeof getAllCalendarsCallback === 'function') {
                getAllCalendarsCallback(callbackData);
            }
        },

        /**
         * 处理生命周期事件回调（由原生代码调用）
         * @param {string} jsonString - 生命周期事件JSON字符串
         */
        handleLifecycleCallback: function(jsonString) {
            try {
                const callbackData = JSON.parse(jsonString);
                console.log('收到生命周期事件回调:', callbackData);

                const result = {
                    event_type: callbackData.event_type,
                    data: callbackData.data,
                    timestamp: new Date(callbackData.data.timestamp * 1000) // 转换为Date对象
                };

                console.log('解析后的生命周期事件数据:', result);

                // 触发生命周期事件回调
                if (lifecycleCallback && typeof lifecycleCallback === 'function') {
                    lifecycleCallback(result);
                }

            } catch (error) {
                console.error('解析生命周期事件回调数据失败:', error);
            }
        },

        /**
         * 初始化API（页面加载时调用）
         */
        init: function() {
            console.log('iBox API 初始化完成，版本:', this.version);

            // 确保回调函数在全局作用域中可用
            window.wxLoginCallback = this.handleWeixinLoginCallback.bind(this);
            window.calendarCallback = this.handleCalendarCallback.bind(this);
            window.deleteCalendarCallback = this.handleDeleteCalendarCallback.bind(this);
            window.getAllCalendarsCallback = this.handleGetAllCalendarsCallback.bind(this);
            window.deviceInfoCallback = this.handleDeviceInfoCallback.bind(this);
            window.lifecycleCallback = this.handleLifecycleCallback.bind(this);
        }
    };
})();

// 页面加载完成后自动初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        window.iBoxAPI.init();
    });
} else {
    window.iBoxAPI.init();
}
