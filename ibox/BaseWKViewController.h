//
//  BaseWKViewController.h
//  ibox
//
//  Created by apple on 2025/7/9.
//

#import <UIKit/UIKit.h>
#import <WebKit/WebKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface BaseWKViewController : UIViewController <WKUIDelegate, WKNavigationDelegate>

// WebView 属性
@property(nonatomic, strong) WKWebView *webview;
// router 属性
@property(nonatomic, copy) NSString *router;

// 基础方法
- (void)setupWebView;
- (void)setupWebViewConstraints;
- (void)configureWebViewSettings;

// 工具方法
- (void)closeModal;

// 可重写的方法
- (BOOL)shouldHandleNavigationAction:(WKNavigationAction *)navigationAction;

@end

NS_ASSUME_NONNULL_END
