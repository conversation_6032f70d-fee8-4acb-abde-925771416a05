//
//  iboxTools.h
//  ibox
//
//  Created by apple on 2025/7/9.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <WebKit/WebKit.h>
#import <EventKit/EventKit.h>
#import <SafariServices/SafariServices.h>

NS_ASSUME_NONNULL_BEGIN

@interface iboxTools : NSObject

// 单例
+ (instancetype)sharedManager;

// 微信登录
- (void)handleWeixinLoginCallback:(NSURL *)url;
- (void)handleWeixinLoginWithURL:(NSURL *)url;
- (void)processWeixinLoginCallbackURL:(NSURL *)url webView:(WKWebView *)webView;

// 微信分享
- (void)handleWeixinShareWithURL:(NSURL *)url;
- (void)shareToWeixinWithType:(NSString *)type
                        title:(NSString *)title
                   localImage:(nullable UIImage *)localImage
                     imageUrl:(nullable NSString *)imageUrl
                     mediaUrl:(NSString *)mediaUrl
                      content:(NSString *)content
                         wxid:(NSString *)wxid
                           to:(NSString *)to;

// 图片处理
- (void)downloadImageWithURL:(NSString *)urlString
                  completion:(void (^)(UIImage *_Nullable image, NSError *_Nullable error))completion;
- (NSData *)dataWithImage:(UIImage *)image scale:(CGSize)size;

// 日历功能
- (void)handleCalendarReminderWithURL:(NSURL *)url webView:(WKWebView *)webView;
- (void)createCalendarEventWithTitle:(NSString *)title
                             content:(NSString *)content
                        startDateStr:(NSString *)startDateStr
                          endDateStr:(NSString *)endDateStr
                         alertOffset:(NSInteger)alertOffset
                            location:(NSString *)location
                          repeatRule:(NSString *)repeatRule
                     reminderMinutes:(NSInteger)reminderMinutes
                         repeatCount:(NSInteger)repeatCount
                             webView:(WKWebView *)webView;
- (void)handleDeleteCalendarWithURL:(NSURL *)url webView:(WKWebView *)webView;
- (void)deleteCalendarEventWithId:(NSString *)eventId webView:(WKWebView *)webView;
- (void)deleteAllCalendarEventsWithWebView:(WKWebView *)webView;
- (void)handleGetAllCalendarsWithURL:(NSURL *)url webView:(WKWebView *)webView;
- (void)saveCalendarEventToLocal:(NSDictionary *)eventData;
- (NSArray *)getAllSavedCalendarEvents;
- (void)removeCalendarEventFromLocal:(NSString *)eventId;
- (void)clearAllLocalCalendarEvents;

// 设备信息
- (void)handleDeviceInfoWithURL:(NSURL *)url webView:(WKWebView *)webView;
- (void)sendCurrentStatusToWebView:(WKWebView *)webView;

#pragma mark - openURL
- (void)handleOpenURLWithURL:(NSURL *)URL;
- (void)handleOpenInAppSafariWithURL:(NSURL *)URL;

// 工具方法
- (NSDictionary *)parseURL:(NSURL *)URL;
- (NSString *)urlDecode:(NSString *)url;
- (NSString *)urlEncode:(NSString *)url;
- (void)openURL:(NSURL *)URL;
- (void)showAlert:(NSString *)title message:(NSString *)message;
- (UIWindow *)currentWindow;
- (CGFloat)bangs;

@end

NS_ASSUME_NONNULL_END
