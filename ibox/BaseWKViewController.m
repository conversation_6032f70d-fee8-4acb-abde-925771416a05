//
//  BaseWKViewController.m
//  ibox
//
//  Created by apple on 2025/7/9.
//

#import "BaseWKViewController.h"
#import "iboxTools.h"

@implementation BaseWKViewController

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.view.backgroundColor = [UIColor blackColor];

    [self setupWebView];
    [self setupWebViewConstraints];
    [self configureWebViewSettings];
}

#pragma mark - WebView Setup

- (void)setupWebView
{
    _webview = [[WKWebView alloc] init];
    _webview.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:_webview];
}

- (void)setupWebViewConstraints
{
    if (!_webview)
    {
        return;
    }

    // 获取安全区域
    UILayoutGuide *safeAreaGuide = self.view.safeAreaLayoutGuide;

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        [_webview.topAnchor constraintEqualToAnchor:safeAreaGuide.topAnchor],
        [_webview.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [_webview.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [_webview.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor]
    ]];
}

- (void)configureWebViewSettings
{
    _webview.backgroundColor = UIColor.clearColor;
    _webview.scrollView.backgroundColor = UIColor.clearColor;
    _webview.opaque = YES;
    _webview.scrollView.bounces = NO;
    _webview.scrollView.showsVerticalScrollIndicator = NO;
    _webview.scrollView.showsHorizontalScrollIndicator = NO;
    _webview.UIDelegate = self;
    _webview.navigationDelegate = self;
    _webview.scrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;

    [UIApplication sharedApplication].idleTimerDisabled = YES;

    if (self.router)
    {
        NSURL *url = [NSURL URLWithString:self.router];
        NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url
                                                               cachePolicy:NSURLRequestReloadIgnoringCacheData
                                                           timeoutInterval:15.0];
        [self.webview loadRequest:request];
    }
}

- (void)setRouter:(NSString *)router
{
    _router = router;
    if (_webview)
    {
        NSURL *url = [NSURL URLWithString:router];
        NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url
                                                               cachePolicy:NSURLRequestUseProtocolCachePolicy
                                                           timeoutInterval:15.0];
        [self.webview loadRequest:request];
    }
}

#pragma mark - WKNavigationDelegate

- (void)webView:(WKWebView *)webView
    decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction
                    decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler
{
    if ([self shouldHandleNavigationAction:navigationAction])
    {
        decisionHandler(WKNavigationActionPolicyCancel);
    }
    else
    {
        decisionHandler(WKNavigationActionPolicyAllow);
    }
}

- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation
{
    // 禁止缩放
    NSString *injectionJSString = @"var script = document.createElement('meta');"
                                   "script.name = 'viewport';"
                                   "script.content=\"width=device-width, user-scalable=no\";"
                                   "document.getElementsByTagName('head')[0].appendChild(script);";
    [webView evaluateJavaScript:injectionJSString completionHandler:nil];

    // 禁止长按选中
    [webView evaluateJavaScript:@"document.documentElement.style.webkitTouchCallout='none';" completionHandler:nil];
    [webView evaluateJavaScript:@"document.documentElement.style.webkitUserSelect='none';" completionHandler:nil];
}

- (BOOL)shouldHandleNavigationAction:(WKNavigationAction *)navigationAction
{
    // 子类重写此方法来处理特定的导航行为
    // 返回 YES 表示已处理，返回 NO 表示继续正常导航

    NSURL *URL = navigationAction.request.URL;
    NSString *scheme = navigationAction.request.URL.scheme;
    NSString *host = [navigationAction.request.URL host];
    NSLog(@"request.URL -> %@", URL);
    NSLog(@"scheme -> %@", scheme);
    NSLog(@"host -> %@", host);

    // 命令链接 - app
    if ([scheme hasPrefix:@"ibox"])
    {
        if ([host isEqualToString:@"openURL"])
        { // 跳转 Safari
            [[iboxTools sharedManager] handleOpenURLWithURL:URL];
        }
        else if ([host isEqualToString:@"openInAppSafari"])
        { // 打开app内的Safari
            [[iboxTools sharedManager] handleOpenInAppSafariWithURL:URL];
        }
        else if ([host isEqualToString:@"weixin_login"])
        { // 微信登录
            [[iboxTools sharedManager] handleWeixinLoginWithURL:URL];
        }
        else if ([host isEqualToString:@"weixin_share"])
        { // 微信分享
            [[iboxTools sharedManager] handleWeixinShareWithURL:URL];
        }
        else if ([host isEqualToString:@"calendar_reminder"])
        { // 日历提醒
            [[iboxTools sharedManager] handleCalendarReminderWithURL:URL webView:self.webview];
        }
        else if ([host isEqualToString:@"device_info"])
        { // 设备信息
            [[iboxTools sharedManager] handleDeviceInfoWithURL:URL webView:self.webview];
        }
        else if ([host isEqualToString:@"current_status"])
        { // 获取当前状态
            [[iboxTools sharedManager] sendCurrentStatusToWebView:self.webview];
        }
        else if ([host isEqualToString:@"delete_calendar"])
        { // 删除日历
            [[iboxTools sharedManager] handleDeleteCalendarWithURL:URL webView:self.webview];
        }
        else if ([host isEqualToString:@"get_all_calendars"])
        { // 获取所有日历
            [[iboxTools sharedManager] handleGetAllCalendarsWithURL:URL webView:self.webview];
        }
        else if ([host isEqualToString:@"close_window"])
        { // 关闭当前界面
            [self closeModal];
        }
        return YES; // 已处理，阻止导航
    }

    return NO; // 未处理，继续正常导航
}

#pragma mark - WKUIDelegate

- (WKWebView *)webView:(WKWebView *)webView
    createWebViewWithConfiguration:(WKWebViewConfiguration *)configuration
               forNavigationAction:(WKNavigationAction *)navigationAction
                    windowFeatures:(WKWindowFeatures *)windowFeatures
{
    // 创建新的ViewController来承载新的WebView
    BaseWKViewController *newViewController = [[BaseWKViewController alloc] init];
    newViewController.router = navigationAction.request.URL.absoluteString;

    // 以模态方式展示
    UINavigationController *modalNavController = [[UINavigationController alloc]
        initWithRootViewController:newViewController];
    modalNavController.modalPresentationStyle = 0;

    // 添加关闭按钮
    UIBarButtonItem *closeButton = [[UIBarButtonItem alloc] initWithTitle:@"关闭" style:UIBarButtonItemStylePlain
                                                                   target:newViewController
                                                                   action:@selector(closeModal)];
    newViewController.navigationItem.leftBarButtonItem = closeButton;

    dispatch_async(dispatch_get_main_queue(),
                   ^{ [self presentViewController:modalNavController animated:YES completion:nil]; });

    return newViewController.webview;
}

- (void)webView:(WKWebView *)webView
    runJavaScriptTextInputPanelWithPrompt:(NSString *)prompt
                              defaultText:(nullable NSString *)defaultText
                         initiatedByFrame:(WKFrameInfo *)frame
                        completionHandler:(void (^)(NSString *__nullable result))completionHandler
{
    // 创建输入提示框
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"输入内容" message:prompt
                                                                      preferredStyle:UIAlertControllerStyleAlert];

    // 添加文本输入框
    [alertController addTextFieldWithConfigurationHandler:^(UITextField *_Nonnull textField) {
        textField.placeholder = @"请输入内容";
        textField.text = defaultText; // 使用默认文本
        textField.clearButtonMode = UITextFieldViewModeWhileEditing;
        textField.borderStyle = UITextBorderStyleRoundedRect;
    }];

    // 确定按钮
    UIAlertAction *confirmAction = [UIAlertAction
        actionWithTitle:@"确定"
                  style:UIAlertActionStyleDefault handler:^(UIAlertAction *_Nonnull action) {
                      UITextField *textField = alertController.textFields.firstObject;
                      NSString *inputText = textField.text ?: @""; // 如果为nil则使用空字符串
                      NSLog(@"用户输入的内容: %@", inputText);
                      completionHandler(inputText);
                  }];

    // 取消按钮
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel
                                                         handler:^(UIAlertAction *_Nonnull action) {
                                                             NSLog(@"用户取消了输入");
                                                             completionHandler(nil); // 取消时返回nil
                                                         }];

    [alertController addAction:cancelAction];
    [alertController addAction:confirmAction];

    // 在主线程中显示提示框
    dispatch_async(dispatch_get_main_queue(),
                   ^{ [self presentViewController:alertController animated:YES completion:nil]; });
}

- (void)webView:(WKWebView *)webView
    runJavaScriptConfirmPanelWithMessage:(NSString *)message
                        initiatedByFrame:(WKFrameInfo *)frame
                       completionHandler:(void (^)(BOOL result))completionHandler
{
    completionHandler(YES);
}

#pragma mark - Utility Methods

- (void)closeModal
{
    [self dismissViewControllerAnimated:YES completion:nil];
}

// 刘海home键延迟滑出-1
- (BOOL)prefersHomeIndicatorAutoHidden
{
    return NO;
}

// 刘海home键延迟滑出-2
- (UIRectEdge)preferredScreenEdgesDeferringSystemGestures
{
    return UIRectEdgeAll;
}

@end
