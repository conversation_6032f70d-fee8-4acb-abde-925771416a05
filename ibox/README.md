# iBox API 使用说明

## 概述

iBox API 是一个用于微信登录、分享、日历提醒等功能的JavaScript接口库。该库将所有功能封装在 `iBoxAPI` 命名空间中，方便其他同事调用。

## 文件结构

```
ibox/
├── ibox-api.js      # API接口文件
├── ibox-demo.html   # 演示页面
└── README.md        # 使用说明
```

## 快速开始

### 1. 引入API文件

在HTML页面中引入API文件：

```html
<script src="ibox-api.js"></script>
```

### 2. 基本使用

API会自动初始化，您可以直接调用相关方法：

```javascript
// 微信登录
iBoxAPI.weixinLogin();

// 分享给好友
iBoxAPI.shareToFriend();

// 分享到朋友圈
iBoxAPI.shareToMoments();

// 创建日历提醒
iBoxAPI.addCalendarReminder();
```

## API 参考

### 核心方法

#### `iBoxAPI.weixinLogin(wxId)`
微信登录功能

**参数：**
- `wxId` (string, 可选): 微信应用ID，默认为 'wxcce32ab520658e7d'

**示例：**
```javascript
iBoxAPI.weixinLogin();
// 或指定微信应用ID
iBoxAPI.weixinLogin('your_wx_id');
```

#### `iBoxAPI.shareToFriend(options)`
分享给微信好友

**参数：**
- `options` (object, 可选): 分享参数对象
  - `title` (string): 分享标题
  - `content` (string): 分享内容
  - `imageUrl` (string): 分享图片URL
  - `mediaUrl` (string): 分享链接URL
  - `wxId` (string): 微信应用ID

**示例：**
```javascript
iBoxAPI.shareToFriend({
    title: '自定义标题',
    content: '自定义内容',
    imageUrl: 'https://example.com/image.jpg',
    mediaUrl: 'https://example.com',
    wxId: 'your_wx_id'
});
```

#### `iBoxAPI.shareToMoments(options)`
分享到微信朋友圈

**参数：**
- `options` (object, 可选): 分享参数对象（同shareToFriend）

#### `iBoxAPI.addCalendarReminder(options)`
创建日历提醒

**参数：**
- `options` (object, 可选): 提醒参数对象
  - `title` (string): 提醒标题
  - `content` (string): 提醒内容
  - `startDate` (Date|string): 开始时间
  - `endDate` (Date|string): 结束时间
  - `alertOffset` (number): 提醒偏移时间（秒），负数表示提前

**示例：**
```javascript
iBoxAPI.addCalendarReminder({
    title: '重要会议',
    content: '项目讨论会议',
    startDate: '2025-07-11T10:00:00',
    endDate: '2025-07-11T11:00:00',
    alertOffset: -900 // 提前15分钟提醒
});
```

### 工具方法

#### `iBoxAPI.getUrlParams()`
获取当前页面的URL参数

**返回：**
- `object`: URL参数对象

#### `iBoxAPI.getIdfaInfo()`
获取IDFA信息

**返回：**
- `object`: IDFA信息对象
  - `idfa`: IDFA值或null
  - `fullUrl`: 完整URL
  - `allParams`: 所有URL参数
  - `hasIdfa`: 是否包含IDFA

### 回调设置

#### `iBoxAPI.setResultCallback(callback)`
设置操作结果回调函数

**参数：**
- `callback` (function): 回调函数，参数为 (message, url)

**示例：**
```javascript
iBoxAPI.setResultCallback(function(message, url) {
    console.log('操作结果:', message, url);
});
```

#### `iBoxAPI.setLoginCallback(callback)`
设置微信登录回调处理函数

**参数：**
- `callback` (function): 回调函数，参数为回调数据对象

**示例：**
```javascript
iBoxAPI.setLoginCallback(function(data) {
    console.log('微信登录回调:', data);
    if (data.code) {
        // 处理登录成功
        console.log('授权码:', data.code);
    } else if (data.error) {
        // 处理登录失败
        console.log('登录失败:', data.error);
    }
});
```

## HTML页面要求

为了正常显示结果和IDFA信息，HTML页面需要包含以下元素：

```html
<!-- 结果显示区域 -->
<div id="result" class="result"></div>

<!-- IDFA信息显示区域 -->
<div id="idfa-info">
    <div><strong>IDFA:</strong> <span id="idfa-value">正在读取...</span></div>
    <div><strong>完整URL:</strong> <div id="full-url">-</div></div>
    <div><strong>URL参数:</strong> <div id="url-params">-</div></div>
</div>
```

## CSS样式

建议添加以下CSS样式以获得更好的显示效果：

```css
.result {
    margin-top: 20px;
    padding: 15px;
    background: #f6f8fa;
    border-radius: 8px;
    font-size: 14px;
    color: #333;
    display: none;
    word-break: break-all;
    word-wrap: break-word;
    white-space: pre-wrap;
    line-height: 1.5;
    max-width: 100%;
    overflow-wrap: break-word;
}

.result.show {
    display: block;
}
```

## 版本信息

当前版本：1.0.0

可通过 `iBoxAPI.version` 获取版本号。

## 注意事项

1. API会在页面加载完成后自动初始化
2. 微信登录回调会自动处理，无需手动设置
3. IDFA信息会自动从URL参数中读取并显示
4. 所有URL调用都会显示在结果区域中
5. 长URL会自动换行，不会影响页面布局

## 示例代码

完整的使用示例请参考 `test.html` 文件。
