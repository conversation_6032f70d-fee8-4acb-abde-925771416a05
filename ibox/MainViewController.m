//
//  ViewController.m
//  ibox
//
//  Created by apple on 2025/7/9.
//

#import "MainViewController.h"
#import "iboxTools.h"
#import <AdSupport/AdSupport.h>
#import <AppTrackingTransparency/AppTrackingTransparency.h>
#import <Network/Network.h>

@interface MainViewController ()
@property(nonatomic, assign) BOOL idfaAuthorizationCompleted;
@property(nonatomic, assign) BOOL networkAvailable;
@property(nonatomic, strong) nw_path_monitor_t networkMonitor;
@property(nonatomic, strong) UIButton *reloadButton;
@end

@implementation MainViewController

- (void)viewDidLoad
{
    [super viewDidLoad];

    // 初始化状态
    self.idfaAuthorizationCompleted = NO;
    self.networkAvailable = NO;

    // 创建重新加载按钮
    [self setupReloadButton];

    // 开始监听网络状态
    [self startNetworkMonitoring];
}

- (void)setupReloadButton
{
    // 创建按钮
    self.reloadButton = [UIButton buttonWithType:UIButtonTypeSystem];

    // 设置按钮样式
    [self.reloadButton setTitle:@"🔄 切换环境" forState:UIControlStateNormal];
    [self.reloadButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.reloadButton.backgroundColor = [UIColor colorWithRed:0.0 green:0.5 blue:1.0 alpha:0.8];
    self.reloadButton.layer.cornerRadius = 8;
    self.reloadButton.titleLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightMedium];

    // 设置按钮内边距
    self.reloadButton.contentEdgeInsets = UIEdgeInsetsMake(8, 12, 8, 12);

    // 添加点击事件
    [self.reloadButton addTarget:self action:@selector(reloadButtonTapped:)
                forControlEvents:UIControlEventTouchUpInside];

    // 设置自动布局
    self.reloadButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.reloadButton];

    // 设置约束 - 放在右上角
    [NSLayoutConstraint activateConstraints:@[
        [self.reloadButton.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor constant:10],
        [self.reloadButton.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-10],
        [self.reloadButton.heightAnchor constraintGreaterThanOrEqualToConstant:36]
    ]];

    NSLog(@"重新加载按钮已创建");
}

- (void)reloadButtonTapped:(UIButton *)sender
{
    NSLog(@"重新加载按钮被点击");
    [self showURLSelectionDialog];
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];

    if (@available(iOS 14.5, *))
    {
        [ATTrackingManager
            requestTrackingAuthorizationWithCompletionHandler:^(ATTrackingManagerAuthorizationStatus status) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    NSLog(@"IDFA授权完成，状态: %ld", (long)status);
                    self.idfaAuthorizationCompleted = YES;
                    [self checkAndLoadContent];
                });
            }];
    }
    else
    {
        NSLog(@"iOS版本低于14.5，跳过IDFA授权");
        self.idfaAuthorizationCompleted = YES;
        [self checkAndLoadContent];
    }
}

- (void)startNetworkMonitoring
{
    if (@available(iOS 12.0, *))
    {
        self.networkMonitor = nw_path_monitor_create();
        nw_path_monitor_set_update_handler(self.networkMonitor, ^(nw_path_t path) {
            BOOL isNetworkAvailable = nw_path_get_status(path) == nw_path_status_satisfied;
            dispatch_async(dispatch_get_main_queue(), ^{
                NSLog(@"网络状态变化: %@", isNetworkAvailable ? @"可用" : @"不可用");
                self.networkAvailable = isNetworkAvailable;
                [self checkAndLoadContent];
            });
        });

        dispatch_queue_t queue = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0);
        nw_path_monitor_set_queue(self.networkMonitor, queue);
        nw_path_monitor_start(self.networkMonitor);
    }
    else
    {
        // iOS 12以下版本，假设网络可用
        NSLog(@"iOS版本低于12.0，跳过网络监听");
        self.networkAvailable = YES;
    }
}

- (void)checkAndLoadContent
{
    NSLog(@"检查授权状态 - IDFA: %@, 网络: %@", self.idfaAuthorizationCompleted ? @"完成" : @"未完成",
          self.networkAvailable ? @"可用" : @"不可用");

    if (self.idfaAuthorizationCompleted && self.networkAvailable)
    {
        NSLog(@"所有权限都已获得，显示URL选择窗口");
        [self showURLSelectionDialog];
    }
}

- (void)showURLSelectionDialog
{
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"选择加载地址"
                                                                             message:@"请选择要加载的环境"
                                                                      preferredStyle:UIAlertControllerStyleActionSheet];

    // Demo本地文件
    UIAlertAction *demoAction = [UIAlertAction
        actionWithTitle:@"Demo本地文件"
                  style:UIAlertActionStyleDefault
                handler:^(UIAlertAction *_Nonnull action) { [self loadContentWithType:@"demo"]; }];

    // 本地开发
    UIAlertAction *localAction = [UIAlertAction
        actionWithTitle:@"本地开发环境"
                  style:UIAlertActionStyleDefault
                handler:^(UIAlertAction *_Nonnull action) { [self loadContentWithType:@"local"]; }];

    // 线上测试
    UIAlertAction *testAction = [UIAlertAction
        actionWithTitle:@"线上测试环境"
                  style:UIAlertActionStyleDefault
                handler:^(UIAlertAction *_Nonnull action) { [self loadContentWithType:@"test"]; }];

    // 线上正式
    UIAlertAction *prodAction = [UIAlertAction
        actionWithTitle:@"线上正式环境"
                  style:UIAlertActionStyleDefault
                handler:^(UIAlertAction *_Nonnull action) { [self loadContentWithType:@"prod"]; }];

    // 手动填写
    UIAlertAction *customAction = [UIAlertAction
        actionWithTitle:@"手动填写地址"
                  style:UIAlertActionStyleDefault
                handler:^(UIAlertAction *_Nonnull action) { [self showCustomURLInput]; }];

    // 取消
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:nil];

    [alertController addAction:demoAction];
    [alertController addAction:localAction];
    [alertController addAction:testAction];
    [alertController addAction:prodAction];
    [alertController addAction:customAction];
    [alertController addAction:cancelAction];

    // iPad适配
    if (alertController.popoverPresentationController)
    {
        alertController.popoverPresentationController.sourceView = self.view;
        alertController.popoverPresentationController.sourceRect = CGRectMake(CGRectGetMidX(self.view.bounds),
                                                                              CGRectGetMidY(self.view.bounds), 0, 0);
        alertController.popoverPresentationController.permittedArrowDirections = 0;
    }

    [self presentViewController:alertController animated:YES completion:nil];
}

- (void)showCustomURLInput
{
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"自定义URL"
                                                                             message:@"请输入要加载的URL地址"
                                                                      preferredStyle:UIAlertControllerStyleAlert];

    [alertController addTextFieldWithConfigurationHandler:^(UITextField *_Nonnull textField) {
        textField.placeholder = @"请输入完整的URL地址";
        textField.text = @"https://";
        textField.keyboardType = UIKeyboardTypeURL;
        textField.clearButtonMode = UITextFieldViewModeWhileEditing;
    }];

    UIAlertAction *confirmAction = [UIAlertAction
        actionWithTitle:@"确定"
                  style:UIAlertActionStyleDefault handler:^(UIAlertAction *_Nonnull action) {
                      UITextField *textField = alertController.textFields.firstObject;
                      NSString *customURL = textField.text;
                      if (customURL && customURL.length > 0)
                      {
                          [self loadContentWithCustomURL:customURL];
                      }
                  }];

    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel
                                                         handler:^(UIAlertAction *_Nonnull action) {
                                                             // 取消后重新显示选择窗口
                                                             [self showURLSelectionDialog];
                                                         }];

    [alertController addAction:cancelAction];
    [alertController addAction:confirmAction];

    [self presentViewController:alertController animated:YES completion:nil];
}

- (void)loadContentWithType:(NSString *)type
{
    NSString *router = @"";

    if ([type isEqualToString:@"demo"])
    {
        // Demo本地文件
        router = [[NSBundle mainBundle] pathForResource:@"ibox-demo" ofType:@"html"];
        if (router)
        {
            router = [NSString stringWithFormat:@"file://%@", router];
        }
        else
        {
            router = @"";
        }
    }
    else if ([type isEqualToString:@"local"])
    {
        // 本地开发
        router = @"http://192.168.132.33:8080/login?debug=100";
    }
    else if ([type isEqualToString:@"test"])
    {
        // 线上测试
        router = @"https://test-ios.box.xxyx.cn/login";
    }
    else if ([type isEqualToString:@"prod"])
    {
        // 线上正式
        router = @"https://dl.box.xxyx.cn/ios_ke/index.html";
    }

    [self loadContentWithURL:router];
}

- (void)loadContentWithCustomURL:(NSString *)customURL
{
    [self loadContentWithURL:customURL];
}

- (void)loadContentWithURL:(NSString *)baseURL
{
    NSString *router = baseURL;

    // 只有网络URL才添加参数
    if ([router hasPrefix:@"http"])
    {
        NSString *idfa = [[[ASIdentifierManager sharedManager] advertisingIdentifier] UUIDString];
        CGFloat bangs = iboxTools.sharedManager.bangs;

        // 检查URL是否已经包含参数
        NSString *separator = [router containsString:@"?"] ? @"&" : @"?";
        router = [NSString stringWithFormat:@"%@%@idfa=%@&bangs=%.2f", router, separator, idfa, bangs];
    }

    NSLog(@"加载URL: %@", router);
    self.router = router;
}

- (void)dealloc
{
    if (self.networkMonitor)
    {
        nw_path_monitor_cancel(self.networkMonitor);
        self.networkMonitor = nil;
    }
}

@end
