//
//  iboxTools.m
//  ibox
//
//  Created by apple on 2025/7/17.
//

#import "iboxTools.h"
#import "BaseWKViewController.h"
@import AdSupport;
@import AppTrackingTransparency;

#import "sys/utsname.h" //utsname
#import <mach/mach.h>
#import <mach/mach_host.h>

@interface iboxTools ()
@property(nonatomic, strong) EKEventStore *eventStore;
@end

@implementation iboxTools

- (instancetype)init
{
    self = [super init];
    if (self)
    {
        // 添加设备生命周期的监听，并通过webview调用js方法
        [self setupLifecycleNotifications];
    }
    return self;
}

- (void)setupLifecycleNotifications
{
    NSNotificationCenter *center = [NSNotificationCenter defaultCenter];

    // 应用生命周期
    [center addObserver:self selector:@selector(applicationDidEnterBackground:)
                   name:UIApplicationDidEnterBackgroundNotification
                 object:nil];
    [center addObserver:self selector:@selector(applicationWillEnterForeground:)
                   name:UIApplicationWillEnterForegroundNotification
                 object:nil];
    [center addObserver:self selector:@selector(applicationDidBecomeActive:)
                   name:UIApplicationDidBecomeActiveNotification
                 object:nil];
    [center addObserver:self selector:@selector(applicationWillResignActive:)
                   name:UIApplicationWillResignActiveNotification
                 object:nil];

    // 内存警告
    [center addObserver:self selector:@selector(applicationDidReceiveMemoryWarning:)
                   name:UIApplicationDidReceiveMemoryWarningNotification
                 object:nil];

    // 设备方向变化
    [center addObserver:self selector:@selector(deviceOrientationDidChange:)
                   name:UIDeviceOrientationDidChangeNotification
                 object:nil];

    // 电池状态监听
    UIDevice *device = [UIDevice currentDevice];
    device.batteryMonitoringEnabled = YES;
    [center addObserver:self selector:@selector(batteryStateDidChange:) name:UIDeviceBatteryStateDidChangeNotification
                 object:nil];
    [center addObserver:self selector:@selector(batteryLevelDidChange:) name:UIDeviceBatteryLevelDidChangeNotification
                 object:nil];

    // 延迟发送初始状态，确保页面已加载
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(),
                   ^{ [self sendInitialLifecycleState]; });
}

- (void)sendInitialLifecycleState
{
    UIDevice *device = [UIDevice currentDevice];

    // 确保电池监控已启用
    device.batteryMonitoringEnabled = YES;

    NSLog(@"=== 发送初始状态 ===");
    NSLog(@"电池状态: %ld", (long)device.batteryState);
    NSLog(@"电池电量: %.2f", device.batteryLevel);
    NSLog(@"设备方向: %ld", (long)device.orientation);

    // 发送初始电池状态
    NSString *batteryState = [self batteryStateToString:device.batteryState];
    NSLog(@"电池状态字符串: %@", batteryState);
    [self notifyLifecycleEvent:@"battery_state_change" data:@{
        @"timestamp" : @([[NSDate date] timeIntervalSince1970]),
        @"battery_state" : batteryState,
        @"battery_level" : @(device.batteryLevel)
    }];

    // 发送初始设备方向
    UIDeviceOrientation orientation = device.orientation;
    NSString *orientationString = [self orientationToString:orientation];
    NSLog(@"设备方向字符串: %@", orientationString);
    [self notifyLifecycleEvent:@"orientation_change" data:@{
        @"timestamp" : @([[NSDate date] timeIntervalSince1970]),
        @"orientation" : orientationString
    }];

    // 发送初始内存状态
    NSNumber *availableMemory = [self getAvailableMemory];
    NSLog(@"可用内存: %@", availableMemory);
    [self notifyLifecycleEvent:@"memory_info" data:@{
        @"timestamp" : @([[NSDate date] timeIntervalSince1970]),
        @"available_memory" : availableMemory
    }];
}

+ (instancetype)sharedManager
{
    static iboxTools *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{ sharedInstance = [[self alloc] init]; });
    return sharedInstance;
}

#pragma mark - 微信登录

- (void)handleWeixinLoginCallback:(NSURL *)url
{
    if (![url.scheme hasPrefix:@"wx"])
    {
        return;
    }
    // 获取当前的ViewController实例
    BaseWKViewController *currentVC =
        (BaseWKViewController *)[iboxTools.sharedManager currentWindow].rootViewController;
    if (currentVC && currentVC.webview)
    {
        // 使用 iboxTools 处理微信登录回调
        [[iboxTools sharedManager] processWeixinLoginCallbackURL:url webView:currentVC.webview];
    }
}

- (void)handleWeixinLoginWithURL:(NSURL *)URL
{
    NSDictionary *ext = [self parseURL:URL];
    if (ext.allKeys.count == 0)
    {
        return;
    }

    NSString *wxId = ext[@"wx_id"];
    NSString *wxUrl = [NSString stringWithFormat:@"weixin://app/%@/auth/?scope=snsapi_userinfo&state=wxAuth", wxId];
    [self openURL:[NSURL URLWithString:wxUrl]];
}

- (void)processWeixinLoginCallbackURL:(NSURL *)url webView:(WKWebView *)webView
{
    // 解析微信回调URL
    NSString *urlString = url.absoluteString;
    NSLog(@"微信登录回调: %@", urlString);

    if (webView)
    {
        // 构造回调数据
        NSMutableDictionary *callbackData = [NSMutableDictionary dictionary];
        callbackData[@"type"] = @"weixin_login_callback";
        callbackData[@"url"] = urlString;

        // 解析URL参数
        NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
        if (components.queryItems)
        {
            for (NSURLQueryItem *item in components.queryItems)
            {
                if (item.value)
                {
                    callbackData[item.name] = item.value;
                }
            }
        }

        // 将回调数据转换为JSON字符串
        NSError *error;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:callbackData options:0 error:&error];
        if (!error && jsonData)
        {
            NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
            // 转义JSON字符串中的引号
            jsonString = [jsonString stringByReplacingOccurrencesOfString:@"\"" withString:@"\\\""];

            // 调用JavaScript回调函数
            NSString *jsCode = [NSString stringWithFormat:@"if(typeof wxLoginCallback === 'function') { "
                                                          @"wxLoginCallback(\"%@\"); }",
                                                          jsonString];

            dispatch_async(dispatch_get_main_queue(), ^{
                [webView evaluateJavaScript:jsCode completionHandler:^(id result, NSError *error) {
                    if (error)
                    {
                        NSLog(@"JavaScript执行错误: %@", error.localizedDescription);
                    }
                    else
                    {
                        NSLog(@"微信登录回调已发送到JavaScript");
                    }
                }];
            });
        }
    }
}

#pragma mark - 微信分享

- (void)handleWeixinShareWithURL:(NSURL *)URL
{
    NSDictionary *ext = [self parseURL:URL];
    if (ext.allKeys.count == 0)
    {
        return;
    }

    NSString *type = ext[@"type"] ?: @"webpage";
    NSString *title = ext[@"title"] ?: @"";
    NSString *imageUrl = ext[@"image_url"] ?: @"";
    NSString *wxId = ext[@"wx_id"];
    NSString *content = ext[@"content"];
    NSString *to = ext[@"to"];
    NSString *mediaUrl = ext[@"media_url"] ?: @"https://www.xxgame.cn";

    // 调用实际的分享方法
    [self shareToWeixinWithType:type title:title localImage:nil imageUrl:imageUrl mediaUrl:mediaUrl content:content
                           wxid:wxId
                             to:to];
}

- (void)shareToWeixinWithType:(NSString *)type
                        title:(NSString *)title
                   localImage:(nullable UIImage *)localImage
                     imageUrl:(nullable NSString *)imageUrl
                     mediaUrl:(NSString *)mediaUrl
                      content:(NSString *)content
                         wxid:(NSString *)wxid
                           to:(NSString *)to
{
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    dic[@"result"] = @"1";
    dic[@"returnFromApp"] = @"1";
    dic[@"scene"] = to;
    dic[@"sdkver"] = @"1.5";
    dic[@"command"] = @"1010";

    if ([type isEqualToString:@"image"])
    {
        if (localImage)
        {
            dispatch_async(dispatch_get_main_queue(), ^{
                // 图片
                dic[@"title"] = title ?: @"";
                dic[@"fileData"] = UIImageJPEGRepresentation(localImage, 1);
                dic[@"thumbData"] = [self dataWithImage:localImage scale:CGSizeMake(100, 100)];
                dic[@"objectType"] = @"2";

                NSData *output = [NSPropertyListSerialization dataWithPropertyList:@{wxid : dic}
                                                                            format:NSPropertyListBinaryFormat_v1_0
                                                                           options:0
                                                                             error:nil];

                [[UIPasteboard generalPasteboard] setData:output forPasteboardType:@"content"];

                NSString *openString = [NSString stringWithFormat:@"weixin://app/%@/sendreq/?", wxid];
                [self openURL:[NSURL URLWithString:openString]];
            });
        }
        else
        {
            [self downloadImageWithURL:imageUrl completion:^(UIImage *image, NSError *error) {
                // 图片
                dic[@"title"] = title ?: @"";
                dic[@"fileData"] = UIImageJPEGRepresentation(image, 1);
                dic[@"thumbData"] = [self dataWithImage:image scale:CGSizeMake(100, 100)];
                dic[@"objectType"] = @"2";

                NSData *output = [NSPropertyListSerialization dataWithPropertyList:@{wxid : dic}
                                                                            format:NSPropertyListBinaryFormat_v1_0
                                                                           options:0
                                                                             error:nil];

                [[UIPasteboard generalPasteboard] setData:output forPasteboardType:@"content"];

                NSString *openString = [NSString stringWithFormat:@"weixin://app/%@/sendreq/?", wxid];
                [self openURL:[NSURL URLWithString:openString]];
            }];
        }
    }
    else if ([type isEqualToString:@"webpage"])
    {
        [self downloadImageWithURL:imageUrl completion:^(UIImage *image, NSError *error) {
            dic[@"description"] = content;
            dic[@"mediaUrl"] = mediaUrl;
            dic[@"objectType"] = @"5";
            dic[@"thumbData"] = [self dataWithImage:image scale:CGSizeMake(100, 100)];
            dic[@"title"] = title;

            NSData *output = [NSPropertyListSerialization dataWithPropertyList:@{wxid : dic}
                                                                        format:NSPropertyListBinaryFormat_v1_0
                                                                       options:0
                                                                         error:nil];

            [[UIPasteboard generalPasteboard] setData:output forPasteboardType:@"content"];

            NSString *openString = [NSString stringWithFormat:@"weixin://app/%@/sendreq/?", wxid];
            [self openURL:[NSURL URLWithString:openString]];
        }];
    }
}

#pragma mark - 图片处理

- (void)downloadImageWithURL:(NSString *)urlString completion:(void (^)(UIImage *image, NSError *error))completion
{
    NSURL *url = [NSURL URLWithString:urlString];
    if (!url)
    {
        if (completion)
        {
            NSError *error = [NSError errorWithDomain:@"ImageDownloadErrorDomain" code:-1
                                             userInfo:@{NSLocalizedDescriptionKey : @"Invalid URL"}];
            completion(nil, error);
        }
        return;
    }

    NSURLSessionConfiguration *config = [NSURLSessionConfiguration defaultSessionConfiguration];
    NSURLSession *session = [NSURLSession sessionWithConfiguration:config];

    NSURLSessionDataTask *task = [session
          dataTaskWithURL:url
        completionHandler:^(NSData *_Nullable data, NSURLResponse *_Nullable response, NSError *_Nullable error) {
            // 处理错误
            if (error)
            {
                [self safeCallCompletion:completion image:nil error:error];
                return;
            }

            // 验证HTTP状态码
            NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
            if (httpResponse.statusCode != 200)
            {
                NSError *statusError = [NSError
                    errorWithDomain:@"ImageDownloadErrorDomain"
                               code:httpResponse.statusCode
                           userInfo:@{
                               NSLocalizedDescriptionKey :
                                   [NSString stringWithFormat:@"HTTP %ld", (long)httpResponse.statusCode]
                           }];
                [self safeCallCompletion:completion image:nil error:statusError];
                return;
            }

            // 转换图片数据
            UIImage *image = [UIImage imageWithData:data];
            if (!image)
            {
                NSError *imageError = [NSError
                    errorWithDomain:@"ImageDownloadErrorDomain"
                               code:-2
                           userInfo:@{NSLocalizedDescriptionKey : @"Failed to decode image data"}];
                [self safeCallCompletion:completion image:nil error:imageError];
                return;
            }

            [self safeCallCompletion:completion image:image error:nil];
        }];

    [task resume];
}

// 确保在主线程回调
- (void)safeCallCompletion:(void (^)(UIImage *, NSError *))completion image:(UIImage *)image error:(NSError *)error
{
    if (!completion)
        return;

    if ([NSThread isMainThread])
    {
        completion(image, error);
    }
    else
    {
        dispatch_async(dispatch_get_main_queue(), ^{ completion(image, error); });
    }
}

- (NSData *)dataWithImage:(UIImage *)image scale:(CGSize)size
{
    UIGraphicsBeginImageContext(size);
    [image drawInRect:CGRectMake(0, 0, size.width, size.height)];
    UIImage *scaledImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return UIImageJPEGRepresentation(scaledImage, 1);
}

#pragma mark - 日历功能

- (EKEventStore *)eventStore
{
    if (!_eventStore)
    {
        _eventStore = [[EKEventStore alloc] init];
    }
    return _eventStore;
}

// 日历提醒
// ibox://calendar_reminder?title=重要提醒&content=这是一个测试提醒事件&start_date=2025-07-10T10:00:00&end_date=2025-07-10T11:00:00&alert_offset=-900
- (void)handleCalendarReminderWithURL:(NSURL *)url webView:(WKWebView *)webView
{
    NSDictionary *ext = [self parseURL:url];
    if (ext.allKeys.count == 0)
    {
        return;
    }

    NSString *title = ext[@"title"] ?: @"提醒";
    NSString *content = ext[@"content"] ?: @"";
    NSString *startDateStr = ext[@"start_date"];
    NSString *endDateStr = ext[@"end_date"];
    NSString *alertOffsetStr = ext[@"alert_offset"] ?: @"-900"; // 默认提前15分钟

    // 请求日历权限
    [self.eventStore requestAccessToEntityType:EKEntityTypeEvent completion:^(BOOL granted, NSError *error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (!granted)
            {
                [self showAlert:@"权限不足" message:@"请在设置中允许访问日历"];
                return;
            }

            if (error)
            {
                [self showAlert:@"错误"
                        message:[NSString stringWithFormat:@"日历访问错误: %@", error.localizedDescription]];
                return;
            }

            // 解析新参数
            NSString *location = ext[@"location"] ?: @"";
            NSString *repeatRule = ext[@"repeat_rule"] ?: @"";
            NSString *reminderMinutesStr = ext[@"reminder_minutes"] ?: @"0";
            NSString *repeatCountStr = ext[@"repeat_count"] ?: @"0";

            // 创建事件
            [self createCalendarEventWithTitle:title content:content startDateStr:startDateStr endDateStr:endDateStr
                                   alertOffset:[alertOffsetStr integerValue]
                                      location:location
                                    repeatRule:repeatRule
                               reminderMinutes:[reminderMinutesStr integerValue]
                                   repeatCount:[repeatCountStr integerValue]
                                       webView:webView];
        });
    }];
}

- (void)createCalendarEventWithTitle:(NSString *)title
                             content:(NSString *)content
                        startDateStr:(NSString *)startDateStr
                          endDateStr:(NSString *)endDateStr
                         alertOffset:(NSInteger)alertOffset
                            location:(NSString *)location
                          repeatRule:(NSString *)repeatRule
                     reminderMinutes:(NSInteger)reminderMinutes
                         repeatCount:(NSInteger)repeatCount
                             webView:(WKWebView *)webView
{
    EKEvent *event = [EKEvent eventWithEventStore:self.eventStore];
    event.title = title;
    // 在notes中添加iBox标识，方便后续识别和管理
    NSString *iboxNotes = content ? [NSString stringWithFormat:@"%@\n\n[Created by iBox]", content]
                                  : @"[Created by iBox]";
    event.notes = iboxNotes;
    event.calendar = [self.eventStore defaultCalendarForNewEvents];

    // 解析日期
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.dateFormat = @"yyyy-MM-dd'T'HH:mm:ss";
    formatter.timeZone = [NSTimeZone localTimeZone];

    NSDate *startDate = nil;
    NSDate *endDate = nil;

    if (startDateStr && startDateStr.length > 0)
    {
        startDate = [formatter dateFromString:startDateStr];
    }

    if (endDateStr && endDateStr.length > 0)
    {
        endDate = [formatter dateFromString:endDateStr];
    }

    // 如果没有提供日期，使用默认值
    if (!startDate)
    {
        startDate = [[NSDate date] dateByAddingTimeInterval:3600]; // 1小时后
    }

    if (!endDate)
    {
        endDate = [startDate dateByAddingTimeInterval:3600]; // 持续1小时
    }

    event.startDate = startDate;
    event.endDate = endDate;

    // 设置位置
    if (location && location.length > 0)
    {
        event.location = location;
    }

    // 设置重复规则，强制设置结束日期（默认一年后）
    if (repeatRule && repeatRule.length > 0)
    {
        NSDate *recurrenceEndDate = [endDate dateByAddingTimeInterval:365 * 24 * 3600]; // 默认一年后
        EKRecurrenceRule *recurrenceRule = [self parseRepeatRule:repeatRule endDate:recurrenceEndDate repeatCount:repeatCount];
        if (recurrenceRule)
        {
            event.recurrenceRules = @[ recurrenceRule ];
        }
    }

    // 添加提醒
    NSMutableArray *alarms = [[NSMutableArray alloc] init];

    // 原有的alertOffset提醒
    if (alertOffset != 0)
    {
        EKAlarm *alarm = [EKAlarm alarmWithRelativeOffset:alertOffset];
        [alarms addObject:alarm];
    }

    // 新的reminderMinutes提醒
    if (reminderMinutes > 0)
    {
        EKAlarm *reminderAlarm = [EKAlarm alarmWithRelativeOffset:-(reminderMinutes * 60)];
        [alarms addObject:reminderAlarm];
    }

    if (alarms.count > 0)
    {
        event.alarms = alarms;
    }

    // 保存事件
    NSError *error = nil;
    BOOL success = [self.eventStore saveEvent:event span:EKSpanThisEvent commit:YES error:&error];

    // 准备回调数据
    NSDictionary *callbackData;
    if (success)
    {
        callbackData = @{
            @"success" : @(YES),
            @"eventId" : event.eventIdentifier ?: @"",
            @"title" : event.title ?: @"",
            @"startDate" : startDate ? @([startDate timeIntervalSince1970]) : @(0),
            @"endDate" : endDate ? @([endDate timeIntervalSince1970]) : @(0)
        };

        // 保存到本地存储
        NSDictionary *eventInfo = @{
            @"eventId" : event.eventIdentifier ?: @"",
            @"title" : event.title ?: @"",
            @"notes" : event.notes ?: @"",
            @"startDate" : startDate ? @([startDate timeIntervalSince1970]) : @(0),
            @"endDate" : endDate ? @([endDate timeIntervalSince1970]) : @(0),
            @"createTime" : @([[NSDate date] timeIntervalSince1970])
        };
        [self saveCalendarEventToLocal:eventInfo];

        //        [self showAlert:@"成功" message:@"日历提醒已创建"];
    }
    else
    {
        callbackData = @{@"success" : @(NO), @"error" : error.localizedDescription ?: @"未知错误"};
        [self showAlert:@"失败"
                message:[NSString stringWithFormat:@"创建日历提醒失败: %@", error.localizedDescription]];
    }

    // 将回调数据转换为JSON字符串
    NSError *jsonError;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:callbackData options:0 error:&jsonError];
    if (!jsonError && jsonData)
    {
        NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];

        // 直接传递JSON字符串给JavaScript，不需要额外转义
        NSString *jsFunc = [NSString
            stringWithFormat:@"if(typeof calendarCallback === 'function') { calendarCallback(%@); }", jsonString];

        NSLog(@"执行JavaScript: %@", jsFunc);
        [webView evaluateJavaScript:jsFunc completionHandler:^(id _Nullable resultValue, NSError *_Nullable error) {
            NSLog(@"日历回调执行结果: %@, 错误: %@", resultValue, error);
        }];
    }
}

// 处理删除日历请求
// ibox://delete_calendar?event_id=xxx 或 ibox://delete_calendar?all=true
- (void)handleDeleteCalendarWithURL:(NSURL *)url webView:(WKWebView *)webView
{
    NSDictionary *ext = [self parseURL:url];
    if (ext.allKeys.count == 0)
    {
        return;
    }

    NSString *eventId = ext[@"event_id"];
    NSString *deleteAll = ext[@"all"];

    if ([deleteAll isEqualToString:@"true"])
    {
        [self deleteAllCalendarEventsWithWebView:webView];
    }
    else if (eventId && eventId.length > 0)
    {
        [self deleteCalendarEventWithId:eventId webView:webView];
    }
    else
    {
        [self showAlert:@"参数错误" message:@"请提供要删除的事件ID或设置all=true删除全部"];
    }
}

// 删除指定日历事件
- (void)deleteCalendarEventWithId:(NSString *)eventId webView:(WKWebView *)webView
{
    if (!eventId || eventId.length == 0)
    {
        [self notifyDeleteCalendarResult:NO eventId:eventId error:@"事件ID不能为空" webView:webView];
        return;
    }

    // 请求日历权限
    [self.eventStore requestAccessToEntityType:EKEntityTypeEvent completion:^(BOOL granted, NSError *error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (!granted)
            {
                [self showAlert:@"权限不足" message:@"请在设置中允许访问日历"];
                [self notifyDeleteCalendarResult:NO eventId:eventId error:@"权限不足" webView:webView];
                return;
            }

            if (error)
            {
                [self showAlert:@"错误"
                        message:[NSString stringWithFormat:@"日历访问错误: %@", error.localizedDescription]];
                [self notifyDeleteCalendarResult:NO eventId:eventId error:error.localizedDescription webView:webView];
                return;
            }

            // 查找并删除事件
            EKEvent *event = [self.eventStore eventWithIdentifier:eventId];
            if (event)
            {
                NSError *deleteError = nil;
                // 如果是重复事件，删除整个系列
                EKSpan span = event.recurrenceRules.count > 0 ? EKSpanFutureEvents : EKSpanThisEvent;
                BOOL success = [self.eventStore removeEvent:event span:span commit:YES error:&deleteError];

                if (success)
                {
                    // 从本地存储中也删除
                    [self removeCalendarEventFromLocal:eventId];
                    //                    [self showAlert:@"成功" message:@"日历事件已删除"];
                    [self notifyDeleteCalendarResult:YES eventId:eventId error:nil webView:webView];
                }
                else
                {
                    [self showAlert:@"失败" message:[NSString stringWithFormat:@"删除日历事件失败: %@",
                                                                               deleteError.localizedDescription]];
                    [self notifyDeleteCalendarResult:NO eventId:eventId error:deleteError.localizedDescription
                                             webView:webView];
                }
            }
            else
            {
                // 从本地存储中也删除
                [self removeCalendarEventFromLocal:eventId];
                [self showAlert:@"未找到" message:@"未找到指定的日历事件"];
                [self notifyDeleteCalendarResult:NO eventId:eventId error:@"未找到指定的日历事件" webView:webView];
            }
        });
    }];
}

// 删除包含"微游咖"的日历事件
- (NSInteger)deleteWYKCalendarEvents
{
    // 时间跨度设置为过去10年到未来10年，确保所有重复事件都能被查到
    NSDate *startDate = [NSDate dateWithTimeIntervalSinceNow:-15 * 365 * 24 * 3600];
    NSDate *endDate = [NSDate dateWithTimeIntervalSinceNow:30 * 365 * 24 * 3600];
    NSArray *allCalendars = [self.eventStore calendarsForEntityType:EKEntityTypeEvent];
    NSPredicate *predicate = [self.eventStore predicateForEventsWithStartDate:startDate endDate:endDate
                                                                    calendars:allCalendars];
    NSArray *events = [self.eventStore eventsMatchingPredicate:predicate];
    NSLog(@"[iBox] 在指定时间范围内找到 %ld 个事件。", (long)events.count);
    NSInteger deletedCount = 0;

    for (int i = 0; i < events.count; i++) {
        EKEvent *event = events[i];
        // 打印前20个事件的标题以供调试
        if (i < 20) {
            NSLog(@"[iBox] 正在检查事件: '%@'", event.title);
        }

        if ([event.title containsString:@"微游咖"])
        {
            NSLog(@"[iBox] 找到要删除的'微游咖'事件: '%@'", event.title);
            [self.eventStore removeEvent:event span:EKSpanFutureEvents commit:NO error:nil];
            deletedCount++;
        }
    }

    NSLog(@"[iBox] 标记为删除的'微游咖'事件总数: %ld", (long)deletedCount);
    return deletedCount;
}

// 删除所有日历事件
- (void)deleteAllCalendarEventsWithWebView:(WKWebView *)webView
{
    [self.eventStore requestAccessToEntityType:EKEntityTypeEvent completion:^(BOOL granted, NSError *error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (!granted)
            {
                [self showAlert:@"权限不足" message:@"请在设置中允许访问日历"];
                [self notifyDeleteCalendarResult:NO eventId:@"all" error:@"权限不足" webView:webView];
                return;
            }
            if (error)
            {
                [self showAlert:@"错误"
                        message:[NSString stringWithFormat:@"日历访问错误: %@", error.localizedDescription]];
                [self notifyDeleteCalendarResult:NO eventId:@"all" error:error.localizedDescription webView:webView];
                return;
            }

            // 首先删除所有包含"微游咖"的事件
            NSInteger wykDeletedCount = [self deleteWYKCalendarEvents];

            // 获取所有本地保存的事件
            NSArray *savedEvents = [self getAllSavedCalendarEvents];
            NSInteger localDeletedCount = 0;

            for (NSDictionary *eventInfo in savedEvents)
            {
                NSString *eventId = eventInfo[@"eventId"];
                if (eventId)
                {
                    EKEvent *event = [self.eventStore eventWithIdentifier:eventId];
                    if (event)
                    {
                        // 为了简单起见，我们忽略单个删除的错误，最后统一提交
                        [self.eventStore removeEvent:event span:EKSpanFutureEvents commit:NO error:nil];
                        localDeletedCount++;
                    }
                }
            }

            NSError *commitError = nil;
            BOOL commitSuccess = [self.eventStore commit:&commitError];

            if (commitSuccess)
            {
                // 清空本地存储
                [self clearAllLocalCalendarEvents];
                NSString *message = [NSString stringWithFormat:@"成功删除了 %ld 个本地日历事件，以及 %ld 个包含‘微游咖’的重复事件。",
                                   (long)localDeletedCount, (long)wykDeletedCount];
                [self showAlert:@"成功" message:message];
                [self notifyDeleteCalendarResult:YES eventId:@"all" error:nil webView:webView];
            }
            else
            {
                NSString *errorMessage = commitError ? commitError.localizedDescription : @"提交日历更改时发生未知错误。";
                [self showAlert:@"失败" message:[NSString stringWithFormat:@"删除日历事件失败: %@", errorMessage]];
                [self notifyDeleteCalendarResult:NO eventId:@"all" error:errorMessage webView:webView];
            }
        });
    }];
}

// 通知删除结果
- (void)notifyDeleteCalendarResult:(BOOL)success
                           eventId:(NSString *)eventId
                             error:(NSString *)error
                           webView:(WKWebView *)webView
{
    NSDictionary *callbackData = @{@"success" : @(success), @"eventId" : eventId ?: @"", @"error" : error ?: @""};

    // 将回调数据转换为JSON字符串
    NSError *jsonError;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:callbackData options:0 error:&jsonError];
    if (!jsonError && jsonData)
    {
        NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];

        // 直接传递JSON字符串给JavaScript
        NSString *jsFunc = [NSString
            stringWithFormat:@"if(typeof deleteCalendarCallback === 'function') { deleteCalendarCallback(%@); }",
                             jsonString];

        NSLog(@"执行删除日历JavaScript: %@", jsFunc);
        [webView evaluateJavaScript:jsFunc completionHandler:^(id _Nullable resultValue, NSError *_Nullable error) {
            NSLog(@"删除日历回调执行结果: %@, 错误: %@", resultValue, error);
        }];
    }
}

#pragma mark - 日历本地存储管理

// 保存日历事件到本地
- (void)saveCalendarEventToLocal:(NSDictionary *)eventData
{
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSMutableArray *savedEvents = [[defaults arrayForKey:@"iBoxCalendarEvents"] mutableCopy];
    if (!savedEvents)
    {
        savedEvents = [[NSMutableArray alloc] init];
    }

    [savedEvents addObject:eventData];
    [defaults setObject:savedEvents forKey:@"iBoxCalendarEvents"];
    [defaults synchronize];

    NSLog(@"日历事件已保存到本地，总数: %lu", (unsigned long)savedEvents.count);
}

// 获取所有已保存的日历事件
- (NSArray *)getAllSavedCalendarEvents
{
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSArray *savedEvents = [defaults arrayForKey:@"iBoxCalendarEvents"];
    return savedEvents ?: @[];
}

// 从本地存储中删除指定日历事件
- (void)removeCalendarEventFromLocal:(NSString *)eventId
{
    if (!eventId || eventId.length == 0)
    {
        return;
    }

    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSMutableArray *savedEvents = [[defaults arrayForKey:@"iBoxCalendarEvents"] mutableCopy];
    if (!savedEvents)
    {
        return;
    }

    // 查找并删除匹配的事件
    for (NSInteger i = savedEvents.count - 1; i >= 0; i--)
    {
        NSDictionary *event = savedEvents[i];
        if ([event[@"eventId"] isEqualToString:eventId])
        {
            [savedEvents removeObjectAtIndex:i];
            break;
        }
    }

    [defaults setObject:savedEvents forKey:@"iBoxCalendarEvents"];
    [defaults synchronize];

    NSLog(@"已从本地存储删除事件: %@，剩余: %lu", eventId, (unsigned long)savedEvents.count);
}

// 清空所有本地日历事件
- (void)clearAllLocalCalendarEvents
{
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    [defaults removeObjectForKey:@"iBoxCalendarEvents"];
    [defaults synchronize];

    NSLog(@"已清空所有本地日历事件");
}

// 处理获取所有日历请求
- (void)handleGetAllCalendarsWithURL:(NSURL *)url webView:(WKWebView *)webView
{
    NSArray *savedEvents = [self getAllSavedCalendarEvents];

    // 准备回调数据
    NSDictionary *callbackData = @{@"success" : @(YES), @"events" : savedEvents, @"count" : @(savedEvents.count)};

    // 将回调数据转换为JSON字符串
    NSError *jsonError;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:callbackData options:0 error:&jsonError];
    if (!jsonError && jsonData)
    {
        NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];

        // 直接传递JSON字符串给JavaScript
        NSString *jsFunc = [NSString
            stringWithFormat:@"if(typeof getAllCalendarsCallback === 'function') { getAllCalendarsCallback(%@); }",
                             jsonString];

        [webView evaluateJavaScript:jsFunc completionHandler:^(id _Nullable resultValue, NSError *_Nullable error) {
            if (error)
            {
                NSLog(@"JavaScript执行错误: %@", error.localizedDescription);
            }
            else
            {
                NSLog(@"获取所有日历回调已发送到JavaScript");
            }
        }];
    }
}

#pragma mark - 重复规则解析

- (EKRecurrenceRule *)parseRepeatRule:(NSString *)repeatRule
{
    if (!repeatRule || repeatRule.length == 0)
    {
        return nil;
    }

    EKRecurrenceFrequency frequency;
    NSInteger interval = 1;

    NSString *rule = [repeatRule lowercaseString];

    if ([rule isEqualToString:@"daily"])
    {
        frequency = EKRecurrenceFrequencyDaily;
    }
    else if ([rule isEqualToString:@"weekly"])
    {
        frequency = EKRecurrenceFrequencyWeekly;
    }
    else if ([rule isEqualToString:@"monthly"])
    {
        frequency = EKRecurrenceFrequencyMonthly;
    }
    else if ([rule isEqualToString:@"yearly"])
    {
        frequency = EKRecurrenceFrequencyYearly;
    }
    else if ([rule hasPrefix:@"every_"])
    {
        // 支持 "every_2_days", "every_3_weeks" 等格式
        NSArray *components = [rule componentsSeparatedByString:@"_"];
        if (components.count >= 3)
        {
            interval = [components[1] integerValue];
            NSString *unit = components[2];

            if ([unit hasPrefix:@"day"])
            {
                frequency = EKRecurrenceFrequencyDaily;
            }
            else if ([unit hasPrefix:@"week"])
            {
                frequency = EKRecurrenceFrequencyWeekly;
            }
            else if ([unit hasPrefix:@"month"])
            {
                frequency = EKRecurrenceFrequencyMonthly;
            }
            else if ([unit hasPrefix:@"year"])
            {
                frequency = EKRecurrenceFrequencyYearly;
            }
            else
            {
                return nil; // 不支持的单位
            }
        }
        else
        {
            return nil; // 格式错误
        }
    }
    else
    {
        return nil; // 不支持的重复规则
    }

    EKRecurrenceRule *recurrenceRule = [[EKRecurrenceRule alloc] initRecurrenceWithFrequency:frequency interval:interval
                                                                                         end:nil];
    return recurrenceRule;
}

// 修改2辅助：parseRepeatRule增加endDate参数
- (EKRecurrenceRule *)parseRepeatRule:(NSString *)repeatRule endDate:(NSDate *)endDate repeatCount:(NSInteger)repeatCount
{
    if (!repeatRule || repeatRule.length == 0)
    {
        return nil;
    }
    EKRecurrenceFrequency frequency;
    NSInteger interval = 1;
    NSString *rule = [repeatRule lowercaseString];
    if ([rule isEqualToString:@"daily"])
    {
        frequency = EKRecurrenceFrequencyDaily;
    }
    else if ([rule isEqualToString:@"weekly"])
    {
        frequency = EKRecurrenceFrequencyWeekly;
    }
    else if ([rule isEqualToString:@"monthly"])
    {
        frequency = EKRecurrenceFrequencyMonthly;
    }
    else if ([rule isEqualToString:@"yearly"])
    {
        frequency = EKRecurrenceFrequencyYearly;
    }
    else if ([rule hasPrefix:@"every_"])
    {
        NSArray *components = [rule componentsSeparatedByString:@"_"];
        if (components.count >= 3)
        {
            interval = [components[1] integerValue];
            NSString *unit = components[2];
            if ([unit hasPrefix:@"day"])
            {
                frequency = EKRecurrenceFrequencyDaily;
            }
            else if ([unit hasPrefix:@"week"])
            {
                frequency = EKRecurrenceFrequencyWeekly;
            }
            else if ([unit hasPrefix:@"month"])
            {
                frequency = EKRecurrenceFrequencyMonthly;
            }
            else if ([unit hasPrefix:@"year"])
            {
                frequency = EKRecurrenceFrequencyYearly;
            }
            else
            {
                return nil;
            }
        }
        else
        {
            return nil;
        }
    }
    else
    {
        return nil;
    }
    EKRecurrenceEnd *recurrenceEnd;
    if (repeatCount > 0) {
        recurrenceEnd = [EKRecurrenceEnd recurrenceEndWithOccurrenceCount:repeatCount];
    } else {
        recurrenceEnd = [EKRecurrenceEnd recurrenceEndWithEndDate:endDate];
    }
    EKRecurrenceRule *recurrenceRule = [[EKRecurrenceRule alloc] initRecurrenceWithFrequency:frequency interval:interval
                                                                                         end:recurrenceEnd];
    return recurrenceRule;
}

#pragma mark - 设备信息

- (void)handleDeviceInfoWithURL:(NSURL *)url webView:(WKWebView *)webView
{
    if (!webView)
    {
        return;
    }

    NSString *appBundleIdentifier = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleIdentifier"];

    NSString *appVersion = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];

    // app name
    NSString *displayName = [[NSBundle mainBundle] localizedInfoDictionary][@"CFBundleDisplayName"];
    if (!displayName)
    {
        displayName = [[NSBundle mainBundle] infoDictionary][@"CFBundleDisplayName"];
    }
    if (!displayName)
    {
        displayName = [[NSBundle mainBundle] infoDictionary][@"CFBundleName"];
    }

    NSString *deviceName = [UIDevice currentDevice].name;
    NSString *deviceIdfa = [ASIdentifierManager sharedManager].advertisingIdentifier.UUIDString;
    NSString *deviceIdfv = [UIDevice currentDevice].identifierForVendor.UUIDString;

    // deviceModel
    struct utsname systemInfo;
    uname(&systemInfo);
    NSString *deviceModel = [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];

    NSString *systemVersion = [UIDevice currentDevice].systemVersion;

    NSString *bangs = [NSString stringWithFormat:@"%.2f", [self bangs]];

    // 启用电池监控并获取电池信息
    UIDevice *device = [UIDevice currentDevice];
    device.batteryMonitoringEnabled = YES;

    // 添加延迟确保电池监控生效
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{});

    NSString *batteryState = [self batteryStateToString:device.batteryState];
    float batteryLevelValue = device.batteryLevel;

    // 如果电池电量为负值，说明获取失败，提供默认值
    if (batteryLevelValue < 0)
    {
        batteryLevelValue = 0.0;
        batteryState = @"unknown";
    }

    NSString *batteryLevel = [NSString stringWithFormat:@"%.0f%%", batteryLevelValue * 100];

    // 获取可用内存
    NSNumber *availableMemory = [self getAvailableMemory];
    NSString *memoryString = [NSString stringWithFormat:@"%.2f MB", [availableMemory doubleValue] / 1024.0 / 1024.0];

    // 获取屏幕尺寸信息
    CGRect screenBounds = [UIScreen mainScreen].bounds;
    CGSize screenSize = screenBounds.size;
    CGFloat screenScale = [UIScreen mainScreen].scale;
    NSString *screenSizeString = [NSString stringWithFormat:@"%.0f×%.0f", screenSize.width, screenSize.height];
    NSString *screenPixelString = [NSString
        stringWithFormat:@"%.0f×%.0f", screenSize.width * screenScale, screenSize.height * screenScale];
    NSString *screenScaleString = [NSString stringWithFormat:@"%.0f", screenScale];

    NSDictionary *deviceInfoDic = @{
        @"app_bundle_id" : appBundleIdentifier,
        @"app_version" : appVersion,
        @"app_name" : displayName,
        @"device_name" : deviceName,
        @"device_idfa" : deviceIdfa,
        @"device_idfv" : deviceIdfv,
        @"device_model" : deviceModel,
        @"device_version" : systemVersion,
        @"device_bangs" : bangs,
        @"battery_state" : batteryState,
        @"battery_level" : batteryLevel,
        @"available_memory" : memoryString,
        @"screen_size" : screenSizeString,
        @"screen_pixel" : screenPixelString,
        @"screen_scale" : screenScaleString
    };

    // 将回调数据转换为JSON字符串
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:deviceInfoDic options:0 error:&error];
    if (!error && jsonData)
    {
        NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];

        // 调用JavaScript回调函数，直接传递JSON对象
        NSString *jsCode = [NSString stringWithFormat:@"if(typeof deviceInfoCallback === 'function') { "
                                                      @"deviceInfoCallback(%@); }",
                                                      jsonString];

        dispatch_async(dispatch_get_main_queue(), ^{
            [webView evaluateJavaScript:jsCode completionHandler:^(id result, NSError *error) {
                if (error)
                {
                    NSLog(@"JavaScript执行错误: %@", error.localizedDescription);
                }
                else
                {
                    NSLog(@"设备信息回调已发送到JavaScript");
                }
            }];
        });
    }
}

#pragma mark - openURL
- (void)handleOpenURLWithURL:(NSURL *)URL
{
    NSDictionary *ext = [self parseURL:URL];
    if (ext.allKeys.count == 0)
    {
        return;
    }
    NSURL *openURL = [NSURL URLWithString:[self urlDecode:ext[@"url"]]];

    [self openURL:openURL];
}

- (void)handleOpenInAppSafariWithURL:(NSURL *)URL
{
    NSDictionary *ext = [self parseURL:URL];
    if (ext.allKeys.count == 0)
    {
        return;
    }
    NSURL *openURL = [NSURL URLWithString:[self urlDecode:ext[@"url"]]];

    if (!openURL)
    {
        [self showAlert:@"错误" message:@"无效的URL地址"];
        return;
    }

    // 获取当前的ViewController实例
    BaseWKViewController *currentVC = (BaseWKViewController *)[iboxTools.sharedManager currentWindow].rootViewController;
    if (!currentVC)
    {
        [self showAlert:@"错误" message:@"无法获取当前视图控制器"];
        return;
    }

    dispatch_async(dispatch_get_main_queue(), ^{
        // 创建SFSafariViewController
        SFSafariViewController *safariVC = [[SFSafariViewController alloc] initWithURL:openURL];

        // 设置Safari视图控制器的样式
        if (@available(iOS 10.0, *)) {
            safariVC.preferredBarTintColor = [UIColor systemBackgroundColor];
            safariVC.preferredControlTintColor = [UIColor systemBlueColor];
        }

        // 以模态方式展示Safari视图控制器
        safariVC.modalPresentationStyle = UIModalPresentationPageSheet;
        [currentVC presentViewController:safariVC animated:YES completion:nil];
    });
}

#pragma mark - 工具方法

- (NSDictionary *)parseURL:(NSURL *)URL
{
    NSArray *array = [[URL query] componentsSeparatedByString:@"&"];

    NSMutableDictionary *ParaDict = [NSMutableDictionary new];

    for (int i = 0; i < [array count]; i++)
    {
        NSArray *keyAndValue = [array[i] componentsSeparatedByString:@"="];

        if ([keyAndValue count] == 2 && keyAndValue[0] && keyAndValue[1])
        {
            [ParaDict setObject:[self urlDecode:keyAndValue[1]] forKey:keyAndValue[0]];
        }
    }
    return ParaDict;
}

- (NSString *)urlDecode:(NSString *)url
{
    return [url stringByRemovingPercentEncoding];
}

- (NSString *)urlEncode:(NSString *)url
{
    NSCharacterSet *allowedCharacters = [[NSCharacterSet characterSetWithCharactersInString:@"!*'();:@&=+$,/?%#[]"]
        invertedSet];
    return [url stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacters];
}

- (void)openURL:(NSURL *)URL
{
    dispatch_async(dispatch_get_main_queue(),
                   ^{ [[UIApplication sharedApplication] openURL:URL options:@{} completionHandler:nil]; });
}

- (void)showAlert:(NSString *)title message:(NSString *)message
{
    dispatch_async(dispatch_get_main_queue(), ^{
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:title message:message
                                                                preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:nil];
        [alert addAction:okAction];

        // 获取当前顶层视图控制器
        UIViewController *topViewController = [self currentWindow].rootViewController;
        if (topViewController)
        {
            [topViewController presentViewController:alert animated:YES completion:nil];
        }
    });
}

- (UIWindow *)currentWindow
{
    UIWindow *window = nil;

    // iOS 13+ 使用scene方式获取window
    if (@available(iOS 13.0, *))
    {
        NSSet<UIScene *> *connectedScenes = [UIApplication sharedApplication].connectedScenes;
        for (UIScene *scene in connectedScenes)
        {
            if ([scene isKindOfClass:[UIWindowScene class]])
            {
                UIWindowScene *windowScene = (UIWindowScene *)scene;
                for (UIWindow *w in windowScene.windows)
                {
                    if (w.isKeyWindow)
                    {
                        window = w;
                        break;
                    }
                }
                if (window)
                    break;
            }
        }
    }

    // 如果没有找到window，使用传统方式（仅在iOS 13以下）
    if (!window)
    {
        if (@available(iOS 13.0, *))
        {
            // iOS 13+ 应该通过scene获取，如果到这里说明没有找到合适的window
            if ([UIApplication sharedApplication].windows.count > 0)
            {
                window = [UIApplication sharedApplication].windows.firstObject;
            }
        }
        else
        {
// iOS 13以下使用keyWindow
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
            window = [UIApplication sharedApplication].keyWindow;
#pragma clang diagnostic pop
            if (!window && [UIApplication sharedApplication].windows.count > 0)
            {
                window = [UIApplication sharedApplication].windows.firstObject;
            }
        }
    }

    return window;
}

- (CGFloat)bangs
{
    return [self currentWindow].safeAreaInsets.top;
}

- (void)sendCurrentStatusToWebView:(WKWebView *)webView
{
    if (!webView)
    {
        return;
    }

    // 立即发送当前状态信息
    UIDevice *device = [UIDevice currentDevice];
    device.batteryMonitoringEnabled = YES;

    NSLog(@"=== 手动获取当前状态 ===");
    NSLog(@"电池监控已启用: %@", device.batteryMonitoringEnabled ? @"是" : @"否");
    NSLog(@"电池状态: %ld", (long)device.batteryState);
    NSLog(@"电池电量: %.2f", device.batteryLevel);
    NSLog(@"设备方向: %ld", (long)device.orientation);

    // 发送当前电池状态
    NSString *batteryState = [self batteryStateToString:device.batteryState];
    NSLog(@"电池状态字符串: %@", batteryState);
    [self notifyLifecycleEventToWebView:webView eventType:@"battery_state_change" data:@{
        @"timestamp" : @([[NSDate date] timeIntervalSince1970]),
        @"battery_state" : batteryState,
        @"battery_level" : @(device.batteryLevel)
    }];

    // 发送当前设备方向
    UIDeviceOrientation orientation = device.orientation;
    NSString *orientationString = [self orientationToString:orientation];
    NSLog(@"设备方向字符串: %@", orientationString);
    [self notifyLifecycleEventToWebView:webView eventType:@"orientation_change" data:@{
        @"timestamp" : @([[NSDate date] timeIntervalSince1970]),
        @"orientation" : orientationString
    }];

    // 发送当前内存状态
    NSNumber *availableMemory = [self getAvailableMemory];
    NSLog(@"可用内存: %@", availableMemory);
    [self notifyLifecycleEventToWebView:webView eventType:@"memory_info" data:@{
        @"timestamp" : @([[NSDate date] timeIntervalSince1970]),
        @"available_memory" : availableMemory
    }];
}

// 通知特定WebView生命周期事件
- (void)notifyLifecycleEventToWebView:(WKWebView *)webView
                            eventType:(NSString *)eventType
                                 data:(NSDictionary *)eventData
{
    if (!webView)
    {
        return;
    }

    NSDictionary *lifecycleData = @{@"event_type" : eventType, @"data" : eventData};

    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:lifecycleData options:0 error:&error];
    if (!error && jsonData)
    {
        NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
        // 转义JSON字符串中的引号
        jsonString = [jsonString stringByReplacingOccurrencesOfString:@"\"" withString:@"\\\""];

        // 调用JavaScript回调函数
        NSString *jsCode = [NSString
            stringWithFormat:@"if(typeof lifecycleCallback === 'function') { lifecycleCallback(\"%@\"); }", jsonString];

        dispatch_async(dispatch_get_main_queue(), ^{
            [webView evaluateJavaScript:jsCode completionHandler:^(id result, NSError *error) {
                if (error)
                {
                    NSLog(@"生命周期事件通知JavaScript执行错误: %@", error.localizedDescription);
                }
                else
                {
                    NSLog(@"生命周期事件已发送到JavaScript: %@", eventType);
                }
            }];
        });
    }
}

#pragma mark - 生命周期回调方法

// 应用生命周期
- (void)applicationDidEnterBackground:(NSNotification *)notification
{
    [self notifyLifecycleEvent:@"background" data:@{@"timestamp" : @([[NSDate date] timeIntervalSince1970])}];
}

- (void)applicationWillEnterForeground:(NSNotification *)notification
{
    [self notifyLifecycleEvent:@"foreground" data:@{@"timestamp" : @([[NSDate date] timeIntervalSince1970])}];
}

- (void)applicationDidBecomeActive:(NSNotification *)notification
{
    [self notifyLifecycleEvent:@"active" data:@{@"timestamp" : @([[NSDate date] timeIntervalSince1970])}];
}

- (void)applicationWillResignActive:(NSNotification *)notification
{
    [self notifyLifecycleEvent:@"inactive" data:@{@"timestamp" : @([[NSDate date] timeIntervalSince1970])}];
}

// 内存警告
- (void)applicationDidReceiveMemoryWarning:(NSNotification *)notification
{
    [self notifyLifecycleEvent:@"memory_warning" data:@{
        @"timestamp" : @([[NSDate date] timeIntervalSince1970]),
        @"available_memory" : [self getAvailableMemory]
    }];
}

// 设备方向变化
- (void)deviceOrientationDidChange:(NSNotification *)notification
{
    UIDeviceOrientation orientation = [UIDevice currentDevice].orientation;
    NSString *orientationString = [self orientationToString:orientation];

    [self notifyLifecycleEvent:@"orientation_change" data:@{
        @"timestamp" : @([[NSDate date] timeIntervalSince1970]),
        @"orientation" : orientationString
    }];
}

// 电池状态变化
- (void)batteryStateDidChange:(NSNotification *)notification
{
    UIDevice *device = [UIDevice currentDevice];
    NSString *batteryState = [self batteryStateToString:device.batteryState];

    [self notifyLifecycleEvent:@"battery_state_change" data:@{
        @"timestamp" : @([[NSDate date] timeIntervalSince1970]),
        @"battery_state" : batteryState,
        @"battery_level" : @(device.batteryLevel)
    }];
}

- (void)batteryLevelDidChange:(NSNotification *)notification
{
    UIDevice *device = [UIDevice currentDevice];

    [self notifyLifecycleEvent:@"battery_level_change" data:@{
        @"timestamp" : @([[NSDate date] timeIntervalSince1970]),
        @"battery_level" : @(device.batteryLevel),
        @"battery_state" : [self batteryStateToString:device.batteryState]
    }];
}

// 通知前端生命周期事件
- (void)notifyLifecycleEvent:(NSString *)eventType data:(NSDictionary *)eventData
{
    // 获取当前的WebView
    BaseWKViewController *currentVC =
        (BaseWKViewController *)[iboxTools.sharedManager currentWindow].rootViewController;
    if (!currentVC || !currentVC.webview)
    {
        return;
    }

    NSDictionary *lifecycleData = @{@"event_type" : eventType, @"data" : eventData};

    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:lifecycleData options:0 error:&error];
    if (!error && jsonData)
    {
        NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
        // 转义JSON字符串中的引号
        jsonString = [jsonString stringByReplacingOccurrencesOfString:@"\"" withString:@"\\\""];

        // 调用JavaScript回调函数
        NSString *jsCode = [NSString
            stringWithFormat:@"if(typeof lifecycleCallback === 'function') { lifecycleCallback(\"%@\"); }", jsonString];

        dispatch_async(dispatch_get_main_queue(), ^{
            [currentVC.webview evaluateJavaScript:jsCode completionHandler:^(id result, NSError *error) {
                if (error)
                {
                    NSLog(@"生命周期事件通知JavaScript执行错误: %@", error.localizedDescription);
                }
                else
                {
                    NSLog(@"生命周期事件已发送到JavaScript: %@", eventType);
                }
            }];
        });
    }
}

// 辅助方法
- (NSString *)orientationToString:(UIDeviceOrientation)orientation
{
    switch (orientation)
    {
    case UIDeviceOrientationPortrait:
        return @"portrait";
    case UIDeviceOrientationPortraitUpsideDown:
        return @"portrait_upside_down";
    case UIDeviceOrientationLandscapeLeft:
        return @"landscape_left";
    case UIDeviceOrientationLandscapeRight:
        return @"landscape_right";
    case UIDeviceOrientationFaceUp:
        return @"face_up";
    case UIDeviceOrientationFaceDown:
        return @"face_down";
    default:
        return @"unknown";
    }
}

- (NSString *)batteryStateToString:(UIDeviceBatteryState)batteryState
{
    switch (batteryState)
    {
    case UIDeviceBatteryStateUnplugged:
        return @"unplugged";
    case UIDeviceBatteryStateCharging:
        return @"charging";
    case UIDeviceBatteryStateFull:
        return @"full";
    default:
        return @"unknown";
    }
}

- (NSNumber *)getAvailableMemory
{
    vm_size_t page_size;
    mach_port_t mach_host = mach_host_self();
    vm_statistics_data_t vm_stat;
    mach_msg_type_number_t host_size = sizeof(vm_statistics_data_t) / sizeof(natural_t);

    host_page_size(mach_host, &page_size);
    host_statistics(mach_host, HOST_VM_INFO, (host_info_t)&vm_stat, &host_size);

    unsigned long long free_memory = (unsigned long long)vm_stat.free_count * page_size;
    return @(free_memory);
}

- (void)dealloc
{
    // 移除所有通知监听
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end
